# 百度搜索爬虫 - 定时任务功能使用说明

## 🎯 项目介绍

这是一个功能强大的百度搜索爬虫工具，**新增了定时任务功能**，让您可以设置自动执行的爬虫任务！

### 主要功能

-   🔍 **百度搜索爬取**：批量爬取百度搜索结果
-   ⏰ **定时任务**：自动定时执行爬虫（重点新功能！）
-   📊 **Web 界面**：可视化操作和管理
-   🔄 **批量处理**：支持多 URL 同时处理
-   🚫 **智能去重**：避免重复数据
-   📁 **数据导出**：Excel、CSV、JSON 格式
-   🎯 **内容过滤**：文案和 URL 过滤
-   📈 **实时监控**：进度状态实时显示

## 🚀 快速开始

### 第一步：安装依赖

```bash
pip install APScheduler==3.10.4
pip install -r requirements.txt
```

### 第二步：启动服务

```bash
python start.py
```

### 第三步：打开浏览器

访问：http://127.0.0.1:5000

### 第四步：开始使用

点击页面顶部的标签页进行操作

## ⏰ 定时任务功能详解

### 什么是定时任务？

定时任务让爬虫可以在指定时间自动运行，无需您手动操作！

**适用场景：**

-   每天定时爬取新闻资讯
-   每周爬取招聘信息
-   定期监控特定内容
-   自动化数据收集

### 如何使用定时任务？

#### 步骤 1：准备爬虫配置

1. 点击 **"爬虫配置"** 标签页
2. 在 URL 输入框中输入要爬取的网址：
    ```
    https://www.baidu.com/s?wd=python教程
    https://www.baidu.com/s?wd=机器学习
    ```
3. 点击 **"保存当前配置"**，输入名称如"Python 学习资料"

#### 步骤 2：创建定时任务

1. 点击 **"定时任务"** 标签页
2. 点击 **"创建定时任务"** 按钮
3. 填写任务信息：
    - **任务名称**：每日 Python 学习资料爬取
    - **爬虫配置**：选择"Python 学习资料"
    - **调度类型**：选择执行频率

#### 步骤 3：设置执行时间

选择以下四种模式之一：

**🕘 每日执行**

-   每天固定时间执行
-   设置：小时 09，分钟 00（每天上午 9 点）

**📅 每周执行**

-   每周固定时间执行
-   设置：星期一，上午 08:00

**⏱️ 间隔执行**

-   按固定间隔重复执行
-   设置：每 2 小时执行一次

**⚙️ 自定义 Cron**

-   使用 Cron 表达式精确控制
-   设置：`0 9 * * *`（每天 9 点）

#### 步骤 4：完成创建

1. 可选择过滤配置（文案过滤、URL 过滤）
2. 建议勾选"过滤重复链接"和"任务内去重"
3. 点击 **"创建任务"** 按钮

### 管理您的定时任务

**查看任务列表**

-   任务名称和使用的配置
-   调度类型和执行时间
-   当前状态（启用/禁用）
-   创建时间

**控制任务**

-   **启用/禁用**：随时开关任务
-   **查看执行记录**：查看任务运行历史
-   **删除任务**：删除不需要的任务

**查看结果**

-   **搜索结果页面**：查看所有爬取的数据
-   **执行记录页面**：查看任务执行状态和错误信息

## 📋 实用示例

### 示例 1：每日新闻爬取

```
任务名称：每日科技新闻
URL配置：https://www.baidu.com/s?wd=科技新闻
调度设置：每日执行 08:00
用途：每天早上自动获取最新科技新闻
```

### 示例 2：每周招聘信息

```
任务名称：程序员招聘信息
URL配置：https://www.baidu.com/s?wd=程序员招聘
调度设置：每周执行 周一 09:00
用途：每周一获取最新招聘信息
```

### 示例 3：实时监控

```
任务名称：股价实时监控
URL配置：https://www.baidu.com/s?wd=股价+实时
调度设置：间隔执行 每2小时
用途：定期监控股价变化
```

### 示例 4：学术论文追踪

```
任务名称：AI论文追踪
URL配置：https://www.baidu.com/s?wd=人工智能+论文+最新
调度设置：自定义Cron 0 */6 * * *（每6小时）
用途：持续追踪最新AI研究论文
```

## 🔧 Cron 表达式说明

Cron 表达式格式：`分 时 日 月 星期`

**常用示例：**

-   `0 9 * * *` - 每天 9 点
-   `0 9 * * 1` - 每周一 9 点
-   `0 */6 * * *` - 每 6 小时
-   `30 8 1 * *` - 每月 1 号 8:30
-   `0 9 * * 1-5` - 每周一到周五 9 点

## 💡 使用技巧

### 合理设置执行频率

-   **新闻类内容**：每天 1-2 次
-   **招聘信息**：每天 1 次或每周几次
-   **实时数据**：每小时 1 次
-   **学术内容**：每天或每周 1 次

### 优化爬取效果

-   **启用去重功能**：避免重复数据
-   **设置过滤规则**：只获取相关内容
-   **合理设置时间**：避免网络高峰期
-   **定期检查结果**：确保任务正常运行

### 监控任务状态

-   **查看执行记录**：了解任务运行情况
-   **关注失败任务**：及时处理错误
-   **调整配置**：根据结果优化设置

## ❓ 常见问题解答

### Q1：任务创建后不执行怎么办？

**A1：** 检查以下几点：

1. 确认任务状态为"启用"（绿色标签）
2. 检查调度时间设置是否正确
3. 查看执行记录中的错误信息
4. 确认系统时间正确

### Q2：如何修改已创建的任务？

**A2：** 目前需要：

1. 删除原任务
2. 重新创建新任务
3. 未来版本会支持直接编辑

### Q3：任务执行失败怎么办？

**A3：** 处理步骤：

1. 查看执行记录中的具体错误信息
2. 检查网络连接是否正常
3. 确认目标网站是否可访问
4. 检查爬虫配置 URL 是否正确
5. 尝试手动执行测试

### Q4：可以同时运行多个任务吗？

**A4：** 可以，但建议：

1. 避免同时运行过多任务
2. 错开执行时间
3. 监控系统性能
4. 合理控制频率

### Q5：数据保存在哪里？

**A5：** 数据保存位置：

1. SQLite 数据库文件（crawler.db）
2. 可在"搜索结果"页面查看
3. 支持导出为 Excel、CSV、JSON 格式

## 🧪 功能测试

### 快速测试

```bash
python 快速测试.py
```

### 完整测试

```bash
python test_scheduler.py
```

### 演示脚本

```bash
python demo_scheduler.py
```

## 📚 更多文档

-   📋 **[5 分钟快速入门](快速入门指南.md)** - 最简单的上手教程
-   🎬 **[操作演示脚本](操作演示脚本.md)** - 详细操作步骤
-   🔧 **[技术实现文档](SCHEDULER_README.md)** - 技术细节说明

## 🎉 开始使用

现在您已经了解了定时任务的完整用法，可以：

1. **立即尝试**：创建第一个定时任务
2. **逐步优化**：根据需要调整配置
3. **持续监控**：定期查看执行结果

**记住**：定时任务会在后台自动运行，您只需要设置一次，就可以持续获得最新数据！

---

💡 **提示**：建议从简单的每日任务开始，熟悉流程后再创建更复杂的任务。如有问题，请查看执行记录中的详细信息。
