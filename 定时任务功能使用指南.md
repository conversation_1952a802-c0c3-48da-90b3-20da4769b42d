# 百度搜索爬虫定时任务功能 - 完整使用指南

## 🎯 功能介绍

定时任务功能让您的百度搜索爬虫可以在指定时间自动运行，无需手动操作！

**主要特点：**
- ⏰ 自动定时执行爬虫任务
- 📅 支持多种时间调度模式
- 🔧 使用已保存的爬虫配置
- 📊 完整的执行历史记录
- 🎛️ 可视化管理界面

## 🚀 快速开始

### 第1步：安装依赖
```bash
pip install APScheduler==3.10.4
```

### 第2步：启动服务
```bash
python start.py
```
> 注意：启动文件是 `start.py`，不是 `main.py`

### 第3步：打开浏览器
访问：http://127.0.0.1:5000
> 注意：端口是 5000，不是 5511

### 第4步：进入定时任务页面
点击页面顶部的 **"定时任务"** 标签页

## 📖 详细使用教程

### 步骤1：准备爬虫配置

在创建定时任务之前，您需要先有一个爬虫配置：

1. **进入爬虫配置页面**
   - 点击 **"爬虫配置"** 标签页

2. **设置URL列表**
   - 在URL输入框中输入要爬取的搜索URL
   - 例如：
     ```
     https://www.baidu.com/s?wd=python教程
     https://www.baidu.com/s?wd=机器学习
     ```

3. **保存配置**
   - 点击 **"保存当前配置"** 按钮
   - 输入配置名称（如："Python学习资料"）
   - 点击保存

### 步骤2：创建定时任务

1. **进入定时任务页面**
   - 点击页面顶部的 **"定时任务"** 标签

2. **点击创建按钮**
   - 点击右上角的 **"创建定时任务"** 按钮

3. **填写基本信息**
   ```
   任务名称：Python教程定时爬取
   爬虫配置：选择刚才保存的"Python学习资料"配置
   ```

4. **选择调度类型**

   **🕘 每日执行**
   ```
   调度类型：每日执行
   小时：09
   分钟：00
   说明：每天上午9点执行
   ```

   **📅 每周执行**
   ```
   调度类型：每周执行
   星期：星期一
   小时：08
   分钟：00
   说明：每周一上午8点执行
   ```

   **⏱️ 间隔执行**
   ```
   调度类型：间隔执行
   间隔数值：2
   间隔类型：小时
   说明：每2小时执行一次
   ```

   **⚙️ 自定义Cron**
   ```
   调度类型：自定义Cron
   Cron表达式：0 */6 * * *
   说明：每6小时执行一次
   ```

5. **可选配置**
   - 文案过滤配置：选择已保存的过滤规则（可选）
   - URL过滤配置：选择已保存的URL过滤规则（可选）
   - 过滤重复链接：✅ 建议勾选
   - 任务内去重：✅ 建议勾选

6. **创建任务**
   - 点击 **"创建任务"** 按钮
   - 看到"定时任务创建成功"提示

### 步骤3：管理定时任务

创建完成后，您可以在任务列表中看到刚创建的任务：

**任务列表显示信息：**
- 任务名称
- 使用的配置
- 调度类型和时间
- 当前状态（启用/禁用）
- 创建时间

**可执行操作：**
- **✅ 启用/禁用**：点击对应按钮来启用或禁用任务
- **📊 执行记录**：点击查看任务运行历史
- **🗑️ 删除**：删除不需要的任务

### 步骤4：查看执行结果

1. **查看执行记录**
   - 在任务列表中点击 **"执行记录"** 按钮
   - 可以看到：
     - 执行时间
     - 执行状态（运行中/已完成/失败）
     - 抓取结果数量
     - 错误信息（如果有）

2. **查看爬取数据**
   - 点击 **"搜索结果"** 标签页
   - 可以看到定时任务爬取的所有数据
   - 数据包括：标题、链接、摘要、发布单位等

## 🔧 调度类型详解

### 每日执行
- **适用场景**：每天定时爬取新闻、公告等
- **设置方法**：选择小时（0-23）和分钟（0-59）
- **示例**：每天上午9点执行

### 每周执行  
- **适用场景**：每周定时爬取周报、统计数据等
- **设置方法**：选择星期几 + 具体时间
- **示例**：每周一上午8点执行

### 间隔执行
- **适用场景**：需要频繁更新的数据
- **设置方法**：设置间隔数值和单位（分钟/小时/天）
- **示例**：每2小时执行一次

### 自定义Cron
- **适用场景**：复杂的时间规则
- **格式**：`分 时 日 月 星期`
- **常用示例**：
  - `0 9 * * *` - 每天9点
  - `0 9 * * 1` - 每周一9点  
  - `0 */6 * * *` - 每6小时
  - `30 8 1 * *` - 每月1号8:30

## 💡 实用示例

### 示例1：每日新闻爬取
```
任务名称：每日科技新闻
URL配置：https://www.baidu.com/s?wd=科技新闻
调度设置：每日执行 08:00
用途：每天早上自动获取最新科技新闻
```

### 示例2：每周招聘信息
```
任务名称：程序员招聘信息
URL配置：https://www.baidu.com/s?wd=程序员招聘
调度设置：每周执行 周一 09:00
用途：每周一获取最新招聘信息
```

### 示例3：实时监控
```
任务名称：股价实时监控
URL配置：https://www.baidu.com/s?wd=股价+实时
调度设置：间隔执行 每2小时
用途：定期监控股价变化
```

## 💡 使用技巧

### 合理设置执行频率
- **新闻爬取**：每天1-2次就够了
- **招聘信息**：每天1次或每周几次
- **实时数据**：每小时1次
- **避免过于频繁**：以免对目标网站造成压力

### 使用过滤配置
- 设置文案过滤：过滤掉不相关的内容
- 设置URL过滤：只爬取特定域名的链接
- 启用去重功能：避免重复数据

### 监控执行状态
- 定期查看执行记录
- 关注失败的任务并及时处理
- 根据结果调整爬虫配置

## ❓ 常见问题

### Q1：任务创建后不执行怎么办？
**A1：** 检查以下几点：
1. 任务是否已启用
2. 调度时间设置是否正确
3. 查看执行记录中的错误信息

### Q2：如何修改已创建的任务？
**A2：** 目前需要删除原任务，重新创建新任务

### Q3：任务执行失败怎么办？
**A3：** 
1. 查看执行记录中的错误信息
2. 检查网络连接
3. 确认目标网站是否可访问
4. 检查爬虫配置是否正确

### Q4：可以同时运行多个任务吗？
**A4：** 可以，但建议避免同时运行过多任务，以免影响系统性能

## 🧪 功能测试

### 快速测试
```bash
python 快速测试.py
```

### 完整测试
```bash
python test_scheduler.py
```

### 演示脚本
```bash
python demo_scheduler.py
```

## 🎉 开始使用

现在您已经掌握了定时任务的完整用法，可以：

1. **立即尝试**：创建第一个定时任务
2. **逐步优化**：根据需要调整配置
3. **持续监控**：定期查看执行结果

**记住**：定时任务会在后台自动运行，您只需要设置一次，就可以持续获得最新数据！

---

💡 **重要提示**：
- 启动文件是 `start.py`
- 访问地址是 http://127.0.0.1:5000
- 定时任务会自动调用 `/api/start` 接口执行爬虫
