import os
import sys
import webbrowser
import time
import threading
from web_gui import app

def start_server():
    """启动Web服务器并自动打开浏览器"""
    # 设置端口
    port = 5001
    
    # 启动浏览器
    def open_browser():
        time.sleep(1)  # 等待服务器启动
        webbrowser.open(f'http://127.0.0.1:{port}')
    
    # 创建templates目录（如果不存在）
    if not os.path.exists('templates'):
        os.makedirs('templates')
    
    # 确保index.html存在
    index_path = os.path.join('templates', 'index.html')
    if not os.path.exists(index_path):
        with open(index_path, 'w', encoding='utf-8') as f:
            f.write('''<!DOCTYPE html>
<html>
<head>
    <title>百度搜索爬虫配置</title>
    <style>
        body {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        textarea {
            width: 100%;
            height: 300px;
            margin: 10px 0;
        }
        .button-group {
            margin: 10px 0;
        }
        button {
            padding: 8px 16px;
            margin-right: 10px;
        }
        #status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background-color: #dff0d8; }
        .error { background-color: #f2dede; }
    </style>
</head>
<body>
    <h1>百度搜索爬虫配置</h1>
    
    <div>
        <label for="configName">配置名称:</label>
        <input type="text" id="configName" value="默认配置">
    </div>

    <div>
        <label for="urls">搜索URL列表 (每行一个):</label>
        <textarea id="urls"></textarea>
    </div>

    <div class="button-group">
        <button onclick="loadConfig()">加载配置</button>
        <button onclick="saveConfig()">保存配置</button>
        <button onclick="startCrawler()">开始爬取</button>
    </div>

    <div id="status"></div>

    <script>
        // 加载最新配置
        async function loadConfig() {
            try {
                const response = await fetch('/api/config');
                const configs = await response.json();
                if (configs.length > 0) {
                    document.getElementById('configName').value = configs[0].name;
                    document.getElementById('urls').value = configs[0].urls.join('\\n');
                    showStatus('配置加载成功', 'success');
                }
            } catch (error) {
                showStatus('加载配置失败: ' + error, 'error');
            }
        }

        // 保存配置
        async function saveConfig() {
            const name = document.getElementById('configName').value;
            const urls = document.getElementById('urls').value
                .split('\\n')
                .map(url => url.trim())
                .filter(url => url);

            try {
                const response = await fetch('/api/config', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({name, urls})
                });
                const result = await response.json();
                if (result.status === 'success') {
                    showStatus('配置已保存', 'success');
                }
            } catch (error) {
                showStatus('保存配置失败: ' + error, 'error');
            }
        }

        // 启动爬虫
        async function startCrawler() {
            const urls = document.getElementById('urls').value
                .split('\\n')
                .map(url => url.trim())
                .filter(url => url);

            try {
                const response = await fetch('/api/start', {
                    method: 'POST',
                    headers: {'Content-Type': 'application/json'},
                    body: JSON.stringify({urls})
                });
                const result = await response.json();
                if (result.status === 'started') {
                    showStatus('爬虫已启动', 'success');
                }
            } catch (error) {
                showStatus('启动爬虫失败: ' + error, 'error');
            }
        }

        function showStatus(message, type) {
            const status = document.getElementById('status');
            status.textContent = message;
            status.className = type;
        }

        // 页面加载时加载最新配置
        loadConfig();
    </script>
</body>
</html>''')
    
    # 启动浏览器线程
    browser_thread = threading.Thread(target=open_browser)
    browser_thread.daemon = True
    browser_thread.start()
    
    # 启动Flask应用
    app.run(port=port, debug=False)

if __name__ == '__main__':
    try:
        start_server()
    except KeyboardInterrupt:
        print("\n服务器已停止")
        sys.exit(0) 