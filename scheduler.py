import json
import threading
import time
from datetime import datetime, timedelta
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger
from database import Database
from browser_search import BaiduCrawler
from config import Config


class TaskScheduler:
    def __init__(self):
        self.scheduler = BackgroundScheduler()
        self.db = Database()
        self.running_tasks = {}  # 存储正在运行的任务
        self.scheduler.start()
        
    def start(self):
        """启动调度器并加载所有启用的任务"""
        self.load_all_tasks()
        
    def stop(self):
        """停止调度器"""
        self.scheduler.shutdown()
        
    def load_all_tasks(self):
        """加载所有启用的定时任务"""
        tasks = self.db.get_scheduled_tasks()
        for task in tasks:
            if task[5]:  # enabled字段
                self.add_task_to_scheduler(task)
                
    def add_task_to_scheduler(self, task):
        """将任务添加到调度器"""
        task_id = task[0]
        schedule_type = task[3]
        schedule_config = json.loads(task[4])
        
        # 移除已存在的任务
        try:
            self.scheduler.remove_job(f"task_{task_id}")
        except:
            pass
            
        # 根据调度类型创建触发器
        trigger = self.create_trigger(schedule_type, schedule_config)
        if trigger:
            self.scheduler.add_job(
                func=self.execute_task,
                trigger=trigger,
                id=f"task_{task_id}",
                args=[task_id],
                max_instances=1,  # 防止任务重复执行
                replace_existing=True
            )
            
    def create_trigger(self, schedule_type, schedule_config):
        """根据调度类型和配置创建触发器"""
        try:
            if schedule_type == 'daily':
                # 每日执行
                hour = schedule_config.get('hour', 9)
                minute = schedule_config.get('minute', 0)
                return CronTrigger(hour=hour, minute=minute)
                
            elif schedule_type == 'weekly':
                # 每周执行
                day_of_week = schedule_config.get('day_of_week', 1)  # 1=Monday
                hour = schedule_config.get('hour', 9)
                minute = schedule_config.get('minute', 0)
                return CronTrigger(day_of_week=day_of_week, hour=hour, minute=minute)
                
            elif schedule_type == 'interval':
                # 间隔执行
                interval_type = schedule_config.get('interval_type', 'hours')
                interval_value = schedule_config.get('interval_value', 1)
                
                if interval_type == 'minutes':
                    return IntervalTrigger(minutes=interval_value)
                elif interval_type == 'hours':
                    return IntervalTrigger(hours=interval_value)
                elif interval_type == 'days':
                    return IntervalTrigger(days=interval_value)
                    
            elif schedule_type == 'cron':
                # 自定义cron表达式
                cron_expr = schedule_config.get('cron_expression', '0 9 * * *')
                # 解析cron表达式
                parts = cron_expr.split()
                if len(parts) == 5:
                    minute, hour, day, month, day_of_week = parts
                    return CronTrigger(
                        minute=minute,
                        hour=hour,
                        day=day,
                        month=month,
                        day_of_week=day_of_week
                    )
                    
        except Exception as e:
            print(f"创建触发器失败: {e}")
            
        return None
        
    def execute_task(self, task_id):
        """执行定时任务"""
        if task_id in self.running_tasks:
            print(f"任务 {task_id} 正在运行中，跳过本次执行")
            return
            
        # 获取任务详情
        task = self.db.get_scheduled_task(task_id)
        if not task:
            print(f"任务 {task_id} 不存在")
            return
            
        # 检查任务是否仍然启用
        if not task[5]:  # enabled字段
            print(f"任务 {task_id} 已被禁用")
            return
            
        # 标记任务为运行中
        self.running_tasks[task_id] = True
        
        # 创建执行记录
        execution_key = f"SCHEDULED_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{task_id:05X}"
        execution_id = self.db.save_scheduled_execution(task_id, execution_key)
        
        def run_crawler():
            try:
                print(f"开始执行定时任务: {task[1]} (ID: {task_id})")
                print(f"任务数据: task[12] = {task[12]}, 类型: {type(task[12])}")

                # 获取配置
                try:
                    # task[12] 是 cc.urls，可能是JSON字符串或已经是列表
                    if task[12]:
                        if isinstance(task[12], str):
                            config_urls = json.loads(task[12])
                            print(f"从JSON字符串解析得到URLs: {config_urls}")
                        elif isinstance(task[12], list):
                            config_urls = task[12]
                            print(f"直接使用列表URLs: {config_urls}")
                        else:
                            config_urls = []
                            print(f"未知类型，使用空列表")
                    else:
                        config_urls = []
                        print(f"task[12]为空，使用空列表")
                except (json.JSONDecodeError, TypeError) as e:
                    raise Exception(f"配置URL解析失败: {str(e)}, 原始数据: {task[12]}")

                if not config_urls:
                    raise Exception("配置URL列表为空")
                
                # 准备爬虫参数
                crawler_params = {
                    'urls': config_urls,
                    'skipDuplicate': bool(task[8]),  # skip_duplicate
                    'skipTaskDuplicate': bool(task[9]),  # skip_task_duplicate
                    'filterConfigId': task[6] if task[6] else None,  # filter_config_id
                    'urlFilterConfigId': task[7] if task[7] else None  # url_filter_config_id
                }
                
                # 创建日志回调
                def log_callback(msg):
                    print(f"[定时任务 {task_id}] {msg}")
                
                # 获取过滤配置
                filter_config = None
                url_filter_config = None
                
                if crawler_params.get('filterConfigId'):
                    # 这里需要从数据库获取过滤配置，暂时设为None
                    pass
                    
                if crawler_params.get('urlFilterConfigId'):
                    # 这里需要从数据库获取URL过滤配置，暂时设为None
                    pass
                
                # 创建爬虫实例
                crawler = BaiduCrawler(
                    log_callback=log_callback,
                    skip_duplicate=crawler_params['skipDuplicate'],
                    skip_task_duplicate=crawler_params['skipTaskDuplicate'],
                    filter_config=filter_config,
                    url_filter_config=url_filter_config
                )
                
                # 保存执行记录
                config_snapshot = json.dumps(crawler_params, ensure_ascii=False)
                self.db.start_execution(execution_key, config_snapshot=config_snapshot)
                
                # 运行爬虫
                results = crawler.run(config_urls)

                # 保存结果
                results_count = 0
                if results and isinstance(results, list):
                    self.db.save_results(results, execution_key)
                    results_count = len(results)
                    print(f"[定时任务 {task_id}] 已保存 {results_count} 条结果")
                else:
                    print(f"[定时任务 {task_id}] 爬虫未返回有效结果")
                
                # 更新执行记录为成功
                self.db.update_scheduled_execution(execution_id, 'completed', results_count=results_count)
                print(f"定时任务 {task[1]} (ID: {task_id}) 执行完成")
                
            except Exception as e:
                error_msg = str(e)
                print(f"定时任务 {task_id} 执行失败: {error_msg}")
                # 更新执行记录为失败
                self.db.update_scheduled_execution(execution_id, 'failed', error_message=error_msg)
                
            finally:
                # 移除运行标记
                if task_id in self.running_tasks:
                    del self.running_tasks[task_id]
        
        # 在新线程中运行爬虫
        thread = threading.Thread(target=run_crawler)
        thread.daemon = True
        thread.start()
        
    def add_task(self, name, config_id, schedule_type, schedule_config, 
                 filter_config_id=None, url_filter_config_id=None,
                 skip_duplicate=True, skip_task_duplicate=True):
        """添加新的定时任务"""
        task_id = self.db.save_scheduled_task(
            name, config_id, schedule_type, json.dumps(schedule_config),
            filter_config_id, url_filter_config_id,
            skip_duplicate, skip_task_duplicate
        )
        
        # 添加到调度器
        task = self.db.get_scheduled_task(task_id)
        if task:
            self.add_task_to_scheduler(task)
            
        return task_id
        
    def update_task_status(self, task_id, enabled):
        """更新任务状态"""
        self.db.update_scheduled_task_status(task_id, enabled)
        
        if enabled:
            # 重新加载任务
            task = self.db.get_scheduled_task(task_id)
            if task:
                self.add_task_to_scheduler(task)
        else:
            # 从调度器中移除任务
            try:
                self.scheduler.remove_job(f"task_{task_id}")
            except:
                pass
                
    def delete_task(self, task_id):
        """删除任务"""
        # 从调度器中移除
        try:
            self.scheduler.remove_job(f"task_{task_id}")
        except:
            pass
            
        # 从数据库中删除
        self.db.delete_scheduled_task(task_id)
        
        # 移除运行标记
        if task_id in self.running_tasks:
            del self.running_tasks[task_id]
            
    def get_task_status(self, task_id):
        """获取任务状态"""
        return {
            'running': task_id in self.running_tasks,
            'scheduled': self.scheduler.get_job(f"task_{task_id}") is not None
        }


# 全局调度器实例
task_scheduler = TaskScheduler()
