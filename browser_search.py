from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriver<PERSON>ait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import json
import os
from datetime import datetime
import time
import random
import re
import base64  # 使用Python内置的base64模块
from config import Config
from database import Database
from urllib.parse import urlparse
import requests
from urllib.parse import parse_qs
import csv
from util import fix_url_to_baidu
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
import urllib3
from bs4 import BeautifulSoup
import hashlib
import platform
import io
from io import BytesIO
from PIL import Image, ImageDraw    
from selenium.webdriver.common.action_chains import ActionChains

urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)


class BaiduCrawler:
    def __init__(self, log_callback=None, skip_blacklist=True, skip_duplicate=True, skip_task_duplicate=True, filter_config=None, url_filter_config=None):
        self.log_callback = log_callback
        self.execution_key = self.generate_execution_key()
        
        # 创建日志文件路径
        base_dir = 'logs'
        if not os.path.exists(base_dir):
            os.makedirs(base_dir)
        self.log_file = os.path.join(base_dir, f'crawler_{self.execution_key}.log')
        
        self.driver = None
        self.db = Database()
        self.running = True
        self.skip_blacklist = skip_blacklist
        self.skip_duplicate = skip_duplicate
        self.skip_task_duplicate = skip_task_duplicate
        
        # 设置默认的标题过滤配置
        self.title_filter = {
            'enabled': False,
            'whitelist': [],
            'blacklist': [],
            'whitelist_required': True
        }
        
        # 设置标题过滤
        if filter_config:
            self.title_filter.update({
                'enabled': filter_config.get('enabled', False),
                'whitelist': filter_config.get('whitelist', []),
                'blacklist': filter_config.get('blacklist', []),
                'whitelist_required': filter_config.get('whitelist_required', True)
            })
        
        # 设置URL过滤
        if url_filter_config:
            self.url_filter_config = {
                'enabled': url_filter_config.get('enabled', False),
                'whitelist': url_filter_config.get('whitelist', []),
                'blacklist': url_filter_config.get('blacklist', [])
            }
        else:
            self.url_filter_config = None
        
        # 记录driver配置
        self.driver_options = None
        self.driver_service_path = None
        self.driver_manager_mode = None  # 'manager'/'local'/'system'
        
        self.log(f"初始化爬虫，执行标识: {self.execution_key}")

    def generate_execution_key(self):
        """生成执行唯一标识"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        random_suffix = ''.join(random.choices('0123456789ABCDEF', k=5))
        return f"EXEC_{timestamp}_{random_suffix}"

    def run(self, urls):
        """运行爬虫"""
        try:
            # 确保记录执行开始
            self.db.start_execution(self.execution_key)
            
            # 发送微信工作机器人消息
            self.send_wx_work_bot("爬虫开始运行")
            
            self.log("正在初始化Chrome浏览器...")
            self.init_browser()
            self.log("浏览器初始化完成")
            
            # 创建保存目录
            base_dir = 'save_data'
            if not os.path.exists(base_dir):
                os.makedirs(base_dir)
            
            save_dir = os.path.join(base_dir, self.execution_key)
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)
            self.log(f"创建数据保存目录: {save_dir}")
            
            # 记录配置信息
            self.log(f"当前配置:")
            self.log(f"- 重复链接: {self.skip_duplicate}")
            self.log(f"- 任务重复链接: {self.skip_task_duplicate}")
            self.log(f"- 标题过滤: {self.title_filter}")
            self.log(f"- URL过滤: {self.url_filter_config}")
            
            all_results = []  # 存储所有结果
            seen_links = set()  # 用于跟踪已经处理过的链接


            # 添加标题过滤设置
            if hasattr(self, 'title_filter'):
                if self.title_filter.get('enabled', False):
                    self.log(f"已启用标题过滤，黑名单: {len(self.title_filter.get('blacklist', []))}个关键词，白名单: {len(self.title_filter.get('whitelist', []))}个关键词")
            
            for idx, url in enumerate(urls, 1):
                if not self.running:
                    self.log("收到停止信号，正在保存当前数据...")
                    break
                
                self.log(f"\n开始处理第 {idx}/{len(urls)} 个配置URL")
                try:
                    # 预热：先访问百度高级搜索页面
                    self.log("预热：访问百度高级搜索页面...")
                    self.driver.get("https://www.baidu.com/gaoji/advanced.html")
                    time.sleep(random.uniform(2, 4))  # 随机等待2-4秒
                    self.log("预热完成，准备访问目标URL")
                    
                    # orginal_url
                    original_url = url
                    url = fix_url_to_baidu(url)
                    self.log(f"处理URL: {url}")
                    
                    self.driver.delete_all_cookies()
                    results = self.process_url(url)
                    
                    if results:
                        # 根据配置决定是否过滤重复链接
                        if not self.skip_duplicate:
                            self.log("已禁用过滤重复链接")
                            filtered_results = results
                            for result in filtered_results:
                                result['search_url'] = url
                        else:
                            self.log("已启用过滤重复链接")
                            filtered_results = []
                            for result in results:
                                if not self.db.get_by_link(result['link']):
                                    result['search_url'] = url
                                    filtered_results.append(result)
                                else:
                                    self.log(f"跳过已存在的链接: {result['link']}")
                        
                        # 过滤当前任务中的重复链接
                        task_filtered_results = []
                        for result in filtered_results:
                            if result['link'] not in seen_links:
                                seen_links.add(result['link'])
                                # 保存original_url
                                result['search_url'] = original_url
                                task_filtered_results.append(result)
                            else:
                                self.log(f"跳过当前任务中重复的链接: {result['link']}")
                        
                        if task_filtered_results:
                            # 添加到总结果
                            all_results.extend(task_filtered_results)
                        else:
                            self.log("所有结果都已存在或重复，跳过保存")
                    else:
                        self.log("未获取到结果")
                    
                except Exception as e:
                    self.log(f"处理URL时出错: {str(e)}")
                    continue

            # 处理和保存所有结果
            self.process_and_save_results(all_results, save_dir)
            
        except Exception as e:
            self.log(f"爬虫运行出错: {str(e)}")
            return []
        finally:
            if self.driver:
                self.driver.quit()
                self.log("浏览器已关闭")

    def init_browser(self):
        """初始化浏览器配置"""
        self.log("开始初始化浏览器...")
        
        try:
            # 更完整的Chrome选项
            options = webdriver.ChromeOptions()
            
            # 随机选择一个User-Agent
            user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
                "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0",
                "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36"
            ]
            options.add_argument(f'user-agent={random.choice(user_agents)}')
            
            # 基本配置
            options.add_argument('--no-sandbox')  # 禁用沙箱模式
            options.add_argument('--disable-dev-shm-usage')  # 禁用/dev/shm使用
            options.add_argument('--disable-gpu')  # 禁用GPU加速
            options.add_argument('--disable-extensions')  # 禁用扩展
            options.add_argument('--disable-software-rasterizer')  # 禁用软件光栅化
            options.add_argument('--disable-notifications')  # 禁用通知
            options.add_argument('--disable-infobars')  # 禁用信息栏
            options.add_argument('--window-size=1920,1080')  # 设置窗口大小
            options.add_argument('--ignore-certificate-errors')  # 忽略证书错误
            options.add_argument('--disable-blink-features=AutomationControlled')  # 禁用自动化控制特性
            options.add_argument('--no-proxy-server')  # 禁用代理
            
            # 添加实验性选项
            options.add_experimental_option('excludeSwitches', ['enable-automation'])
            options.add_experimental_option('useAutomationExtension', False)
            
            # 尝试直接指定ChromeDriver路径
            # try:
            #     # 方法1: 使用ChromeDriverManager
            #     self.log("尝试使用ChromeDriverManager初始化...")
            #     self.driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
            #     self.driver_manager_mode = 'manager'
            #     self.driver_service_path = None
            # except Exception as e:
            #     self.log(f"ChromeDriverManager初始化失败: {str(e)}")
                
            #     # 获取是window还是mac
            #     if platform.system().lower() == 'windows':
            #         # 方法3: 尝试指定本地ChromeDriver路径
            #         self.log("尝试使用本地ChromeDriver...")
            #         driver_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chromedriver.exe')
            #         if not os.path.exists(driver_path):
            #             self.log(f"本地ChromeDriver不存在: {driver_path}")
            #             raise Exception("无法找到可用的ChromeDriver")
            #         self.driver = webdriver.Chrome(service=ChromeService(driver_path), options=options)
            #         self.driver_manager_mode = 'local'
            #         self.driver_service_path = driver_path
            #     else:
            #         # 方法2: 尝试使用系统中已安装的ChromeDriver
            #         self.log("尝试使用系统ChromeDriver...")
            #         self.driver = webdriver.Chrome(options=options)
            #         self.driver_manager_mode = 'system'
            #         self.driver_service_path = None


            if platform.system().lower() == 'windows':
                # 方法3: 尝试指定本地ChromeDriver路径
                self.log("尝试使用本地ChromeDriver...")
                driver_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'chromedriver.exe')
                if not os.path.exists(driver_path):
                    self.log(f"本地ChromeDriver不存在: {driver_path}")
                    raise Exception("无法找到可用的ChromeDriver")
                self.driver = webdriver.Chrome(service=ChromeService(driver_path), options=options)
                self.driver_manager_mode = 'local'
                self.driver_service_path = driver_path
            else:
                # 方法2: 尝试使用系统中已安装的ChromeDriver
                self.log("尝试使用系统ChromeDriver...")
                self.driver = webdriver.Chrome(options=options)
                self.driver_manager_mode = 'system'
                self.driver_service_path = None
            
            # 记录options
            self.driver_options = options
            
            # 执行JavaScript来修改webdriver标记
            self.driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {
                'source': 'Object.defineProperty(navigator, "webdriver", {get: () => undefined})'
            })
            
            self.log("浏览器初始化成功")
            return True
        except Exception as e:
            self.log(f"浏览器初始化失败: {str(e)}")
            return False

    def handle_slider_verification(self, max_retries=3):
        """处理滑块验证，支持多次重试
        
        Args:
            max_retries (int): 最大重试次数
            
        Returns:
            bool: 验证是否成功
        """
        for attempt in range(max_retries):
            try:
                self.log(f"第 {attempt + 1} 次尝试验证...")
                
                # 检查是否存在滑块验证码
                try:
                    WebDriverWait(self.driver, 5).until(
                        lambda x: x.find_element(By.XPATH, '//img[@class="passMod_spin-background"]')
                    )
                except TimeoutException:
                    self.log("没有检测到滑块验证码，验证通过")
                    return True
                    
                self.log("检测到滑块验证码,开始识别...")

                img = self.driver.find_element(By.XPATH, '//img[@class="passMod_spin-background"]')
                img_url = img.get_attribute('src')
                self.log(f'img_url: {img_url}')

                # 下载图片
                header = {
                    "Host": "passport.baidu.com",
                    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:72.0) Gecko/20100101 Firefox/72.0",
                    "Accept": "image/webp,*/*",
                    "Accept-Language": "zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2",
                    "Accept-Encoding": "gzip, deflate, br",
                    "Connection": "keep-alive",
                    "Referer": "https://seccaptcha.baidu.com/v1/webapi/verint/svcp.html?ak=M7bcdh2k6uqtYV5miaRiI8m8x6LIaONq&backurl=https%3A%2F%2Fwenku.baidu.com%2F%3F_wkts_%3D1705066238641&ext=ih2lW9VV3PmxmO%2B%2Bx8wZgk9i1xGx9WH05J9hI74kTEVkpokzRQ8QxLB082MG2VoQUUT15llYBwsC%2BAaysNoPxpuKg0Hkpo4qMzBjXDEGhuQ%3D&subid=pc_home&ts=1705066239&sign=1cebe634245cd92fc9eca10d0850a36b",
                    "Cookie": "BAIDUID=A0621DC238F4D936B38F699B70A7E41F:SL=0:NR=10:FG=1; BIDUPSID=A0621DC238F4D9360CD42C9C31352635; PSTM=1667351865; HOSUPPORT=1; UBI=fi_PncwhpxZ%7ETaKAanh2ue0vFk6vHMY02DgvigILJIFul8Z1nzMr9do3SYLtjAUqHSpUz7LvOKV27cIr18-YJryP0Q8j92oo93%7E6hGa0CLdraAlaHUZG-0PW9QrpZkW7MTyUn-yrAq7OmSRBIJ7%7E8gM9pv-; HISTORY=0ece87e30ec8ecccd52ff3d5c42f98002a893bfb73ff358893; BDUSS_BFESS=kwTVdpeFNORXlWVEozbW1kcFhBeHo0ZWQwbVlJNlBvcFhEWWpRZVJQWGhzbnBsSUFBQUFBJCQAAAAAAAAAAAEAAAC13Mct0KHQwl9keHkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAOElU2XhJVNld1; H_WISE_SIDS=219946_216846_213346_219942_213039_230178_204909_230288_110085_236307_243888_244730_245412_243706_232281_249910_247148_250889_249892_252577_234296_253427_253705_240590_254471_179345_254689_254884_254864_253213_255713_254765_255939_255959_255982_107317_256062_256093_256083_255803_253993_256257_255661_256025_256223_256439_256446_254831_253151_256252_256196_256726_256739_251973_256230_256611_256996_257068_257079_257047_254075_257110_257208_251196_254144_257290_251068_256095_257287_254317_251059_251133_254299_257454_257302_255317_255907_255324_257481_244258_257582_257542_257503_255177_257745_257786_257937_257167_257904_197096_257586_257402_255231_257790_258193_258248_258165_8000084_8000115_8000114_8000126_8000140_8000149_8000166_8000172_8000178_8000181_8000185_8000204; Hm_lvt_90056b3f84f90da57dc0f40150f005d5=1700546200; MAWEBCUID=web_VYfxPuQDaKjEzVgXMFgoHouACkpXyjcDpcWwhATKqELuuwEtNy; BAIDUID_BFESS=A0621DC238F4D936B38F699B70A7E41F:SL=0:NR=10:FG=1; H_PS_PSSID=40206_40215_40080_40352_40379_40416_40300_40466_40471_40317; ZFY=j0lpzcgUac2hW5oc8GUPbnW9ug8zMx:B7VJa:AnxqPUaQ:C; BDRCVFR[gltLrB7qNCt]=mk3SLVN4HKm; delPer=0; PSINO=6",
                }

                self.log('下载验证码图片')
                response = requests.get(url=img_url, headers=header)
                img = Image.open(BytesIO(response.content))
                
                # 识别角度
                self.log('发送到识别接口')
                angle = self.shibie(img)
                if not angle:
                    self.log("角度识别失败，重试...")
                    time.sleep(2)  # 等待2秒后重试
                    continue

                self.log(f'识别结果成功: {angle}')

                # 计算滑动距离
                move_x = int(angle * (238 / 360))
                if move_x >= 238:
                    move_x = 237
                elif move_x < 10:
                    move_x = 10

                # 获取滑块
                self.log('获取滑块')
                tag = WebDriverWait(self.driver, 10).until(
                    EC.presence_of_element_located((By.XPATH, '//div[contains(@class, "passMod_slide-btn")]'))
                )
                self.log(f'滑块: {tag}')

                # 滑动滑块
                action = ActionChains(self.driver)
                action.click_and_hold(tag).perform()
                # 计算实际滑动距离 = 像素距离 + 前面空白距离
                if move_x+11 < 238:
                    action.move_by_offset(move_x+11, 5)
                    action.move_by_offset(-15, -2)
                    action.move_by_offset(4, 3)
                else:
                    action.move_by_offset(move_x - 11, 5)
                    action.move_by_offset(7, -2)
                    action.move_by_offset(4, 3)
                action.release().perform()

                
                
                # 等待一段时间，检查验证是否成功
                time.sleep(2)

                # 如果还有
                try:
                    WebDriverWait(self.driver, 3).until(
                        lambda x: x.find_element(By.XPATH, '//img[@class="passMod_spin-background"]')
                    )
                    # 还有验证码，看看还剩下几次重试
                    if attempt < max_retries - 1:
                        self.log(f"还有验证码，还剩下 {max_retries - attempt - 1} 次重试")
                        time.sleep(2)
                        continue
                    else:
                        # 截取屏幕图片
                        screenshot_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), './logs/screenshot.png')
                        self.driver.save_screenshot(screenshot_path)
                        self.log("没有重试机会了，验证失败")
                        self.send_wx_work_bot(f'智能拉取验证码失败')
                        self.send_image_to_wx_work_bot(screenshot_path)
                    return False
                
                except TimeoutException:
                    self.log("验证成功，没有检测到验证码")
                    # 发送识别结果,拉取成功后发送
                    self.send_wx_work_bot(f'智能拉取验证码成功')
                    return True
                
            except Exception as e:
                self.log(f"验证过程出错: {str(e)}")
                if attempt < max_retries - 1:  # 如果不是最后一次尝试
                    time.sleep(2)  # 等待2秒后重试
                    continue
                return False
        
        # 最后再检查一次是否还有验证码
        try:
            WebDriverWait(self.driver, 3).until(
                lambda x: x.find_element(By.XPATH, '//img[@class="passMod_spin-background"]')
            )
            self.log("验证失败，仍然存在验证码")
            return False
        except TimeoutException:
            self.log("验证成功，没有检测到验证码")
            return True

    def process_url(self, url):
        """处理单个URL"""
        try:
            self.log(f"正在访问页面...")
            self.driver.get(url)
            
            # 添加随机滚动行为，模拟人类浏览
            self.log("模拟页面浏览行为...")
            # self.simulate_human_behavior()
            
            # time.sleep(random.uniform(2, 4))
            
            results = []
            page_num = 1
            
            while self.running:
                self.log(f"\n正在处理第 {page_num} 页")
                try:
                    # 处理滑块验证
                    if not self.handle_slider_verification(max_retries=3):
                        self.log("滑块验证失败，跳过当前页面")
                        break
                    
                    # return [];

                    try:
                        WebDriverWait(self.driver, 3).until(
                            EC.presence_of_element_located((By.CLASS_NAME, "content_none"))
                        )
                        self.log("没有内容，切换到下一个配置")         
                        break
                    except:
                        self.log("正常生成内容")

                    # 等待搜索结果加载
                    self.log("等待搜索结果加载...")
                    WebDriverWait(self.driver, 600).until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, Config.BAIDU['SELECTORS']['RESULT']['CONTAINER']))
                    )
                    
                    # 获取搜索结果
                    page_results = self.extract_results(url)
                    if page_results:
                        self.log(f"本页获取到 {len(page_results)} 条结果")
                        results.extend(page_results)
                    else:
                        self.log("本页未获取到结果")
                        break

                    # 检查是否有下一页
                    try:
                        next_buttons = self.driver.find_elements(By.CSS_SELECTOR, Config.BAIDU['SELECTORS']['PAGINATION']['NEXT_BUTTON'])
                        self.log(f"找到 {len(next_buttons)} 个导航按钮")
                        
                        next_page_button = None
                        for button in next_buttons:
                            self.log(f"按钮文本: {button.text}")
                            if "下一页" in button.text:
                                next_page_button = button
                                break
                        
                        if next_page_button and "n-disable" not in next_page_button.get_attribute("class"):
                            self.log("找到下一页按钮，正在切换...")
                            # 使用JavaScript点击
                            self.driver.execute_script("arguments[0].click();", next_page_button)
                            time.sleep(random.uniform(5, 10))  # 增加等待时间到5-10秒
                            page_num += 1
                        else:
                            self.log("已到达最后一页")
                            break
                            
                    except Exception as e:
                        self.log(f"处理下一页按钮时出错: {str(e)}")
                        break
                        
                except TimeoutException:
                    self.log("页面加载超时")
                    break
                except Exception as e:
                    self.log(f"处理页面时出错: {str(e)}")
                    break
                    
            self.log(f"URL处理完成，共获取 {len(results)} 条结果")
            return results
            
        except Exception as e:
            self.log(f"处理URL时出错: {str(e)}")
            return []

    def simulate_human_behavior(self):
        """模拟人类浏览行为，随机滚动和停顿"""
        try:
            # 随机滚动几次
            for _ in range(random.randint(1, 3)):
                # 随机滚动距离
                scroll_distance = random.randint(100, 500)
                self.driver.execute_script(f"window.scrollBy(0, {scroll_distance});")
                # 随机停顿
                time.sleep(random.uniform(0.5, 2))
                
            # 有时候滚回顶部
            if random.random() > 0.7:
                self.driver.execute_script("window.scrollTo(0, 0);")
                time.sleep(random.uniform(0.5, 1))
        except Exception as e:
            self.log(f"模拟人类行为时出错: {str(e)}")
            # 出错不影响主流程
            pass

    def filter_link_blacklist(self, link):
        """过滤链接黑白名单
        新逻辑：
        1. 如果符合白名单 -> 直接通过，不检查黑名单
        2. 如果不符合白名单或没有白名单 -> 检查是否符合黑名单
        """
        try:
            domain = urlparse(link).netloc
            
            # 如果没有启用URL过滤，直接返回链接
            if not self.url_filter_config or not self.url_filter_config.get('enabled', False):
                return link
            
            # 检查白名单 - 如果匹配白名单中的某个域名，直接通过不检查黑名单
            whitelist = self.url_filter_config.get('whitelist', [])
            if whitelist:
                for w in whitelist:
                    if domain == w['domain']:
                        self.log(f"域名 {domain} 匹配白名单，直接通过")
                        return link
                
                self.log(f"域名 {domain} 不在白名单中")
            
            # 到这里说明不符合白名单或没有白名单，检查黑名单
            blacklist = self.url_filter_config.get('blacklist', [])
            for rule in blacklist:
                # 使用search而不是match,因为match必须从字符串开头匹配
                if re.search(rule['pattern'], link):
                    self.log(f"链接 {link} 匹配黑名单规则 {rule['pattern']}，跳过")
                    return None
            
            # 不在黑名单中，通过
            self.log(f"链接黑白名单过滤通过: {link}")
            return link
        except Exception as e:
            self.log(f"过滤链接时出错: {str(e)}")
            return None

    def extract_results(self, search_url):
        """提取搜索结果"""
        try:
            results = []
            containers = self.driver.find_elements(By.CSS_SELECTOR, Config.BAIDU['SELECTORS']['RESULT']['CONTAINER'])
            
            self.log(f"\n找到 {len(containers)} 个结果容器")
            
            for idx, container in enumerate(containers, 1):
                try:
                    self.log(f"\n处理第 {idx} 个结果:")

                    result = container
                    
                    # 提取标题和链接
                    try:
                        title_elem = result.find_element(By.CSS_SELECTOR, "h3.c-title a")
                    except:
                        # 有可能被反爬了，这个时候用c-title没有了 智能用h3+a来了
                        try:
                            title_elem = result.find_element(By.CSS_SELECTOR, "h3 a")
                            self.log(f"发现h3+a，继续")
                        except:
                            self.log(f"\n发现不符合内容，跳过链接")
                            continue


                    title = title_elem.text.strip()
                    link = title_elem.get_attribute('href')
                    
                    # 提取完整标题
                    full_title = title
                    try:
                        tools_div = result.find_element(By.CLASS_NAME, "c-tools")
                        tools_data = json.loads(tools_div.get_attribute('data-tools').replace("'", '"'))
                        full_title = tools_data.get('title', title)
                    except:
                        pass
                    
                    # 应用标题过滤规则
                    # if hasattr(self, 'title_filter') and self.title_filter.get('enabled', False):
                    #     # 检查标题是否应该被过滤
                    #     if not self.should_keep_title(title, full_title):
                    #         self.log(f"标题被过滤: {title}")
                    #         continue
                    
                    # 提取摘要
                    abstract = ''
                    try:
                        abstract_elem = result.find_element(By.CLASS_NAME, "content-right_1THTn")
                        abstract = abstract_elem.text.strip()
                    except:
                        pass
                    
                    # 提取发布单位
                    publish_unit = ''

                    try:
                        # 找到class为cosc-source-text的元素
                        unit_elem = result.find_element(By.CLASS_NAME, "cosc-source-text")
                        publish_unit = unit_elem.text.strip()
    
                    except:
                        self.log(f"发布单位获取失败")
                        pass

                    # 获取实际的link
                    try:
                        headers = {'User-Agent': random.choice(Config.USER_AGENTS)}
                        response = requests.get(link, headers=headers, allow_redirects=True, timeout=5, verify=False)
                        response.encoding = response.apparent_encoding  # 自动检测编码，防止中文乱码
                        link = response.url
                        # 提取链接里面的tdk
                        html = response.text
                        url_title, url_article_title = self.get_info_from_html(html)
                        # 合并到title里面再
                        if hasattr(self, 'title_filter') and self.title_filter.get('enabled', False):
                            if not self.should_keep_title(url_article_title, full_title):
                                self.log(f"tdk标题被过滤: {url_article_title+full_title}")
                                continue
                    except requests.exceptions.SSLError as e:
                        self.log(f"requests SSL 握手失败，尝试用临时driver获取 TDK: {str(e)}")
                        try:
                            url_title, url_article_title = self.get_info_from_html_with_temp_driver(link)
                            if hasattr(self, 'title_filter') and self.title_filter.get('enabled', False):
                                if not self.should_keep_title(url_article_title, full_title):
                                    self.log(f"tdk标题被过滤: {url_article_title+full_title}")
                                    continue
                        except Exception as se:
                            self.log(f"临时driver 获取 TDK 也失败: {str(se)}")
                            continue
                    except requests.exceptions.ConnectionError as e:
                        self.log(f"requests ConnectionError: {str(e)}")
                        try:
                            url_title, url_article_title = self.get_info_from_html_with_temp_driver(link)
                            if hasattr(self, 'title_filter') and self.title_filter.get('enabled', False):
                                if not self.should_keep_title(url_article_title, full_title):
                                    self.log(f"tdk标题被过滤: {url_article_title+full_title}")
                                    continue
                        except Exception as se:
                            self.log(f"临时driver 获取 TDK 也失败: {str(se)}")
                            continue
                    except Exception as e:
                        str_error = str(e)
                        self.log(f"提取TDK时出错: {str_error}")
                        link = self.extract_url_from_error(str_error)
                        if not link:
                            continue

                    
                    # link 替换 掉无效字符串
                    for replace_title in Config.LINK_REPLACE_TITLES:
                        link = link.replace(replace_title, '')

                    if link:
                        # 如果包含了 (Caused by 和后面的字符串一并替换掉
                        if "(Caused by" in link:
                            link = link.split("(Caused by")[0]

                    # 过滤link黑白名单
                    link = self.filter_link_blacklist(link)
                    if not link:
                        continue


                    # 提取wd
                    wd = self.get_wd_from_url(search_url)    
                    
                    if title and link:
                        result_data = {
                            'title': title,
                            'full_title': full_title,
                            'link': link,
                            'abstract': abstract,
                            'publish_unit': publish_unit,
                            'search_url': search_url,
                            'wd': wd,
                            'url_title':url_title,
                            'url_article_title':url_article_title
                        }
                        self.log(f"完整结果数据: {result_data}")
                        results.append(result_data)
                    else:
                        self.log("标题或链接为空，跳过")
                    
                except Exception as e:
                    self.log(f"处理单个结果时出错: {str(e)}")
                    continue
                
            self.log(f"\n本页共提取到 {len(results)} 条有效结果")
            return results
            
        except Exception as e:
            self.log(f"提取搜索结果时出错: {str(e)}")
            return []

    def should_keep_title(self, title, full_title):
        """检查标题是否应该保留（不被过滤）
        
        新逻辑：
        1. 如果匹配白名单关键词 -> 直接通过，不检查黑名单
        2. 如果不匹配白名单或没有白名单 -> 检查是否匹配黑名单
        """
        if not hasattr(self, 'title_filter'):
            return True
        
        title_filter = self.title_filter
        if not title_filter.get('enabled', False):
            return True
        
        # 合并标题和完整标题进行检查
        combined_title = (title + " " + full_title).lower()
        
        # 首先检查白名单 - 如果匹配任何白名单关键词，直接通过
        whitelist = title_filter.get('whitelist', [])
        if whitelist:
            for keyword in whitelist:
                if keyword.lower() in combined_title:
                    self.log(f"标题匹配白名单关键词 '{keyword}'，直接通过")
                    return True
            
            self.log(f"标题未匹配任何白名单关键词")
        
        # 到这里说明不符合白名单或没有白名单，检查黑名单
        blacklist = title_filter.get('blacklist', [])
        if blacklist:
            for keyword in blacklist:
                if keyword.lower() in combined_title:
                    self.log(f"标题匹配黑名单关键词 '{keyword}'，过滤")
                    return False
        
        # 不在黑名单中，通过
        return True

    def get_wd_from_url(self, url):
        """从URL中提取wd参数的值"""
        try:
            from urllib.parse import urlparse, parse_qs
            parsed_url = urlparse(url)
            params = parse_qs(parsed_url.query)
            
            # 提取q1-q6参数
            search_terms = []
            for i in range(1, 7):
                param = f'q{i}'
                if param in params:
                    search_terms.append(params[param][0])
            
            if search_terms:
                # 将所有搜索词组合
                combined_terms = '+'.join(filter(None, search_terms))
                # 替换不合法的文件名字符
                safe_filename = "title_" + re.sub(r'[\\/:*?"<>|]', '_', combined_terms)
                # 限制文件名长度
                safe_filename = safe_filename[:100] if len(safe_filename) > 100 else safe_filename
                return safe_filename
        except Exception as e:
            self.log(f"提取搜索参数时出错: {str(e)}")
            return f"config_{random.randint(1000, 9999)}"
        return f"config_{random.randint(1000, 9999)}"

    def extract_url_from_error(self, error_str):
        """
        从requests错误信息中提取完整URL
        处理格式：HTTPSConnectionPool(host='...', port=443): ... url: /path...
        """
        # 使用正则匹配关键信息
        pattern = r"host='(.*?)'.*?port=(\d+).*?url: (.*?)\)"
        match = re.search(pattern, str(error_str))
        
        if match:
            host = match.group(1)
            port = match.group(2)
            path = match.group(3).strip("'\" ")  # 去除可能存在的引号
            
            # 处理端口号（443和80默认不显示）
            if port == "443" or port == "80":
                return f"https://{host}{path}"
            else:
                return f"https://{host}:{port}{path}"
        else:
            # 备用匹配模式（处理没有port的情况）
            alt_pattern = r"host='(.*?)'.*?url: (.*?)\)"
            alt_match = re.search(alt_pattern, str(error_str))
            if alt_match:
                return f"https://{alt_match.group(1)}{alt_match.group(2).strip()}"
            return None
        
    def get_info_from_html(self, html):
        """从HTML中提取信息(暂时获取tdk)"""
        try:
            soup = BeautifulSoup(html, 'html.parser')
            url_title = soup.title.string if soup.title else ""
            url_article_title = ""
            
            # 查找ArticleTitle meta标签
            article_title_meta = soup.find("meta", attrs={"name": "ArticleTitle"})
            if article_title_meta and article_title_meta.get('content'):
                url_article_title = article_title_meta.get('content')

            # 清理标题中的空白字符
            url_title = url_title.replace(" ", "").replace("\n", "") if url_title else ""
            url_article_title = url_article_title.replace(" ", "").replace("\n", "") if url_article_title else ""

            # 修改日志输出方式，将多个参数合并成一个字符串
            self.log(f"Title: {url_title}")
            self.log(f"ArticleTitle: {url_article_title}")
            
            return url_title, url_article_title
        except Exception as e:
            self.log(f"提取HTML信息时出错: {str(e)}")
            return '', ''

    def get_info_from_html_with_temp_driver(self, url):
        """用临时driver获取TDK，避免污染主driver，配置与主driver一致"""
        try:
            options = self.driver_options
            temp_driver = None
            if self.driver_manager_mode == 'manager':
                temp_driver = webdriver.Chrome(service=ChromeService(ChromeDriverManager().install()), options=options)
            elif self.driver_manager_mode == 'local':
                temp_driver = webdriver.Chrome(service=ChromeService(self.driver_service_path), options=options)
            else:
                temp_driver = webdriver.Chrome(options=options)
            temp_driver.get(url)
            html = temp_driver.page_source
            soup = BeautifulSoup(html, 'html.parser')
            url_title = soup.title.string if soup.title else ""
            article_title_meta = soup.find("meta", attrs={"name": "ArticleTitle"})
            url_article_title = article_title_meta.get('content') if article_title_meta else ""
            temp_driver.quit()
            # 清理标题中的空白字符
            url_title = url_title.replace(" ", "").replace("\n", "") if url_title else ""
            url_article_title = url_article_title.replace(" ", "").replace("\n", "") if url_article_title else ""
            self.log(f"[临时driver] Title: {url_title}")
            self.log(f"[临时driver] ArticleTitle: {url_article_title}")
            return url_title, url_article_title
        except Exception as e:
            self.log(f"临时driver获取TDK失败: {str(e)}")
            return '', ''

    def add_time_range(self, url):
        """添加时间范围参数"""
        if 'gpc=' in url:
            return url
        
        end_time = int(time.time())
        begin_time = end_time - Config.BAIDU['SEARCH_TIME_RANGE']['DAYS'] * 24 * 60 * 60
        gpc = f'stf={begin_time},{end_time}|stftype=2'
        
        connector = '&' if '?' in url else '?'
        url_with_time = f"{url}{connector}gpc={gpc}"
        
        self.log(f"添加时间范围参数: {gpc}")
        return url_with_time

    def stop(self):
        """停止爬虫"""
        self.log("正在停止爬虫...")
        self.running = False

    def log(self, message):
        """统一的日志输出"""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        formatted_message = f"[{timestamp}] {message}"
        
        print(formatted_message)
        
        # 写到日志文件
        if self.log_file:
            try:
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    f.write(formatted_message + '\n')
            except Exception as e:
                print(f"写入日志文件失败: {str(e)}")

        # 调用回调函数
        if self.log_callback:
            try:
                self.log_callback(message)
            except Exception as e:
                print(f"执行日志回调失败: {str(e)}")

    def save_to_csv(self, results, filename):
        """保存结果到CSV文件"""
        # 定义字段顺序和中文表头
        fields_map = [
            ('full_title', '标题'),
            ('link', '链接'),
            ('publish_unit', '发布单位'),
            ('search_url', '搜索链接'),
            ('current_time', '当前时间'),
            ('url_title', 'url标题'),
            ('url_article_title', 'url文章标题')
        ]
        
        try:
            with open(filename, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.writer(f)
                
                # 写入中文表头
                writer.writerow([header for _, header in fields_map])
                
                # 写入数据
                for result in results:
                    current_time = datetime.now().strftime('%Y-%m-%d %H:%M')
                    # 将current_time添加到result字典中
                    result['current_time'] = current_time
                    # 按字段顺序获取数据
                    row = [result.get(field, '') for field, _ in fields_map]
                    writer.writerow(row)
                
            self.log(f"成功保存 {len(results)} 条结果到: {filename}")
            
        except Exception as e:
            self.log(f"保存CSV文件时出错: {str(e)}")

    def send_wx_work_bot(self, message):
        """发送微信工作机器人消息"""
        url = Config.WECHAT['BOT_URL']
        data = {
            "msgtype": "text",
            "text": {
                "content": self.execution_key + " " + message
            }
        }
        try:
            requests.post(url, json=data, verify=False, timeout=5)
        except Exception as e:
            self.log(f"微信通知发送失败: {str(e)}")
            # 记录错误但继续执行爬虫
            pass

    def send_image_to_wx_work_bot(self, image_path):
        """发送图片到微信工作机器人"""
        url = Config.WECHAT['BOT_URL']
        with open(image_path,"rb") as f:
            fd=f.read()
            base64Content=str(base64.b64encode(fd),"utf-8")
        with open(image_path,"rb") as f:
            fd=f.read()
            md = hashlib.md5()
            md.update(fd)
            md5Content = md.hexdigest()
        headers = {"content-type": "application/json"}
        msg = {"msgtype": "image","image": {"base64": base64Content,"md5": md5Content}}
        try:
            requests.post(url, headers=headers, json=msg, verify=False, timeout=5)
            return True
        except Exception as e:
            self.log(f"微信图片发送失败: {str(e)}")
            return False
        

    def check_internet_connection(self):
        """检查网络连接"""
        try:
            requests.get("https://www.baidu.com", timeout=5)
            return True
        except:
            return False

    def clear_browser_data(self):
        """清除浏览器数据"""
        try:
            self.driver.delete_all_cookies()
            self.driver.execute_script("window.localStorage.clear();")
            self.driver.execute_script("window.sessionStorage.clear();")
            self.log("已清除浏览器数据")
        except Exception as e:
            self.log(f"清除浏览器数据时出错: {str(e)}")

    def process_and_save_results(self, results, save_dir):
        """处理和保存所有结果"""
        if results:
            # 保存结果总数信息到文件，方便前端读取
            result_count_file = os.path.join(save_dir, 'result_count.txt')
            with open(result_count_file, 'w', encoding='utf-8') as f:
                f.write(str(len(results)))
            
            # 保存所有结果到总CSV
            total_csv = os.path.join(save_dir, 'all_results.csv')
            self.save_to_csv(results, total_csv)
            self.log(f"\n所有结果已保存到: {total_csv}")
            self.log(f"\n本次搜索共获取到 {len(results)} 条结果")
            self.log("\n所有URL处理完成")
            self.send_wx_work_bot(f"所有URL处理完成，共获取到 {len(results)} 条结果，文件路径：{total_csv}")
            
            # 保存结果到数据库
            self.db.save_results(results, self.execution_key)
            self.log(f"已将 {len(results)} 条结果保存到数据库")
            
            # 返回结果数量，便于前端使用
            return len(results)
        else:
            self.log("没有结果")
            self.send_wx_work_bot("没有获取到任何一条合适的结果，任务结束")
            
            # 没有结果时返回0
            return 0

    def PIL_base64(self,img, coding='utf-8'):
        img_format = img.format
        if img_format == None:
            img_format = 'JPEG'

        format_str = 'JPEG'
        if 'png' == img_format.lower():
            format_str = 'PNG'
        if 'gif' == img_format.lower():
            format_str = 'gif'

        if img.mode == "P":
            img = img.convert('RGB')
        if img.mode == "RGBA":
            format_str = 'PNG'
            img_format = 'PNG'

        output_buffer = BytesIO()
        # img.save(output_buffer, format=format_str)
        img.save(output_buffer, quality=100, format=format_str)
        byte_data = output_buffer.getvalue()
        base64_str = 'data:image/' + img_format.lower() + ';base64,' + base64.b64encode(byte_data).decode(coding)

        return base64_str
    
    # 接口识别
    def shibie(self,img):
        # 图片转base64
        img_base64 = self.PIL_base64(img)
        # 验证码识别接口
        url = "http://bq1gpmr8.xiaomy.net/openapi/verify_code_identify/"
        data = {
            # 用户的key
            "key": Config.IMG_IDENTIFICATION_KEY,
            # 验证码类型
            "verify_idf_id": "44",
            # 样例图片
            "img_base64": img_base64,
        }
        header = {"Content-Type": "application/json"}

        # 发送请求调用接口
        response = requests.post(url=url, json=data, headers=header)
        # 判断是否正确请求
        if response.json()['code'] == 200:
            self.log(response.json())
            return response.json()['data']['angle']
        else:
            self.log('参数错误，请前往得塔云了解详情：http://bq1gpmr8.xiaomy.net/tool/verifyCodeHomePage2/?_=1714093687434')
            self.log('错误参数：', response.json())
            return None
    
if __name__ == "__main__":
    crawler = BaiduCrawler()
    if crawler.check_internet_connection():
        crawler.run()
    else:
        print("网络连接失败，请检查网络设置")