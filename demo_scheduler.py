#!/usr/bin/env python3
"""
定时任务功能演示脚本
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import Database
from scheduler import TaskScheduler

def create_demo_config():
    """创建演示用的爬虫配置"""
    print("创建演示配置...")
    
    db = Database()
    
    # 创建一个简单的测试配置
    demo_urls = [
        'https://www.baidu.com/s?wd=python+爬虫',
        'https://www.baidu.com/s?wd=定时任务'
    ]
    
    config_id = db.save_config("演示配置", json.dumps(demo_urls, ensure_ascii=False))
    print(f"✓ 创建演示配置，ID: {config_id}")
    
    return config_id

def create_demo_tasks(config_id):
    """创建演示定时任务"""
    print("创建演示定时任务...")
    
    scheduler = TaskScheduler()
    
    # 1. 创建每日任务
    daily_config = {
        'hour': 9,
        'minute': 0
    }
    
    daily_task_id = scheduler.add_task(
        name="每日演示任务",
        config_id=config_id,
        schedule_type="daily",
        schedule_config=daily_config
    )
    print(f"✓ 创建每日任务，ID: {daily_task_id}")
    
    # 2. 创建间隔任务（每5分钟执行一次，仅用于演示）
    interval_config = {
        'interval_type': 'minutes',
        'interval_value': 5
    }
    
    interval_task_id = scheduler.add_task(
        name="间隔演示任务",
        config_id=config_id,
        schedule_type="interval",
        schedule_config=interval_config
    )
    print(f"✓ 创建间隔任务，ID: {interval_task_id}")
    
    # 3. 创建每周任务
    weekly_config = {
        'day_of_week': 1,  # 星期一
        'hour': 10,
        'minute': 30
    }
    
    weekly_task_id = scheduler.add_task(
        name="每周演示任务",
        config_id=config_id,
        schedule_type="weekly",
        schedule_config=weekly_config
    )
    print(f"✓ 创建每周任务，ID: {weekly_task_id}")
    
    return [daily_task_id, interval_task_id, weekly_task_id]

def show_task_status(task_ids):
    """显示任务状态"""
    print("\n当前任务状态:")
    print("-" * 50)
    
    db = Database()
    tasks = db.get_scheduled_tasks()
    
    for task in tasks:
        if task[0] in task_ids:  # task[0] 是 id
            schedule_config = json.loads(task[4])  # task[4] 是 schedule_config
            
            print(f"任务ID: {task[0]}")
            print(f"名称: {task[1]}")
            print(f"类型: {task[3]}")
            print(f"配置: {schedule_config}")
            print(f"状态: {'启用' if task[5] else '禁用'}")
            print(f"创建时间: {task[10]}")
            print("-" * 30)

def monitor_tasks(task_ids, duration=60):
    """监控任务执行（仅监控，不实际执行爬虫）"""
    print(f"\n监控任务执行 {duration} 秒...")
    print("注意：这只是演示，实际的爬虫任务需要在Web界面中启动")
    
    db = Database()
    start_time = time.time()
    
    while time.time() - start_time < duration:
        # 检查执行记录
        executions = db.get_scheduled_executions(limit=10)
        
        if executions:
            print(f"\n[{datetime.now().strftime('%H:%M:%S')}] 最近的执行记录:")
            for exec_record in executions[:3]:  # 只显示最近3条
                print(f"  - 任务: {exec_record[8] or '未知'}")  # task_name
                print(f"    执行ID: {exec_record[2]}")  # execution_key
                print(f"    状态: {exec_record[3]}")  # status
                print(f"    开始时间: {exec_record[4]}")  # start_time
        
        time.sleep(10)  # 每10秒检查一次

def cleanup_demo_tasks(task_ids):
    """清理演示任务"""
    print("\n清理演示任务...")
    
    scheduler = TaskScheduler()
    
    for task_id in task_ids:
        try:
            scheduler.delete_task(task_id)
            print(f"✓ 删除任务 {task_id}")
        except Exception as e:
            print(f"✗ 删除任务 {task_id} 失败: {e}")

def main():
    """主演示函数"""
    print("=" * 60)
    print("百度爬虫定时任务功能演示")
    print("=" * 60)
    
    try:
        # 1. 创建演示配置
        config_id = create_demo_config()
        
        # 2. 创建演示任务
        task_ids = create_demo_tasks(config_id)
        
        # 3. 显示任务状态
        show_task_status(task_ids)
        
        # 4. 提示用户
        print("\n" + "=" * 60)
        print("演示任务已创建！")
        print("请在Web界面 (http://127.0.0.1:5000) 中查看和管理这些任务")
        print("点击'定时任务'标签页可以看到刚创建的任务")
        print("=" * 60)
        
        # 5. 询问是否监控
        response = input("\n是否监控任务状态？(y/n): ").lower().strip()
        if response == 'y':
            monitor_tasks(task_ids)
        
        # 6. 询问是否清理
        response = input("\n是否清理演示任务？(y/n): ").lower().strip()
        if response == 'y':
            cleanup_demo_tasks(task_ids)
            print("✓ 演示任务已清理")
        else:
            print("演示任务保留，可在Web界面中管理")
            
    except Exception as e:
        print(f"演示过程中出错: {e}")
    
    print("\n演示结束")

if __name__ == "__main__":
    main()
