import os
from bs4 import BeautifulSoup
from config import Config


def parse_baidu_html_results(html_path, top_n=3):
    """
    读取百度搜索结果页html，提取前top_n个结果，结构参考browser_search.py的extract_results。
    """
    if not os.path.exists(html_path):
        print(f"文件不存在: {html_path}")
        return []
    with open(html_path, 'r', encoding='utf-8') as f:
        html = f.read()
    soup = BeautifulSoup(html, 'html.parser')
    results = []

    class_CONTAINER = 'result c-container xpath-log new-pmd'


    containers = soup.find_all('div', class_=class_CONTAINER, limit=top_n)

    for container in containers:

        # h3.c-title a
        try:
            title = container.find('h3', class_='c-title a').text.strip()
            print(title)
        except:
            print(container)
            continue

        



def main():
    html_path = 'html1.html'
    parse_baidu_html_results(html_path, top_n=3)
  

if __name__ == '__main__':
    main()
