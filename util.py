import requests
from config import Config
import time
import random
import math
import numpy as np
from typing import List, <PERSON><PERSON>

def upload_file(file_path):
    """
    上传文件到服务器
    
    Args:
        file_path: 要上传的文件路径
        
    Returns:
        dict: 包含上传结果的字典,格式为:
            {
                'id': 文件ID,
                'url': 文件路径,
                'fullUrl': 完整URL,
                'name': 文件名
            }
        如果上传失败返回None
    """
    try:
        # 构建multipart/form-data格式的数据
        files = {
            'file': open(file_path, 'rb')
        }
        
        data = {
            'token': Config.UPLOAD['TOKEN']
        }
        
        # 发送POST请求上传文件
        response = requests.post(
            Config.UPLOAD['API_URL'],
            files=files,
            data=data,
            timeout=30
        )
        
        # 检查响应状态
        response.raise_for_status()
        
        result = response.json()
        
        if result.get('result') == 1:  # 假设0表示成功
            return {
                'url': result['data']['url'], 
            }
        else:
            print(result)
            print(f"上传失败: {result.get('message')}")
            return None
            
    except Exception as e:
        print(f"上传文件时发生错误: {str(e)}")
        return None
    finally:
        # 确保文件被关闭
        if 'files' in locals():
            files['file'].close()

def fix_url_to_baidu(url):
    """修复URL到百度标准格式"""
    
    end_time = int(time.time())
    begin_time = end_time - Config.BAIDU['SEARCH_TIME_RANGE']['DAYS'] * 24 * 60 * 60
    gpc = f'stf={begin_time},{end_time}|stftype=2'
    
    connector = '&' if '?' in url else '?'
    url_with_time = f"{url}{connector}gpc={gpc}"
    
    url = url_with_time

    return url

class SlideTrackGenerator:
    """滑动轨迹生成器，用于生成模拟人类操作的滑动轨迹"""
    
    @staticmethod
    def bezier_curve(points: List[Tuple[float, float]], num_points: int = 50) -> List[Tuple[float, float]]:
        """使用贝塞尔曲线生成平滑的轨迹
        
        Args:
            points: 控制点列表，每个点是(x, y)坐标
            num_points: 生成的轨迹点数量
            
        Returns:
            轨迹点列表，每个点是(x, y)坐标
        """
        def pascal_row(n: int) -> List[int]:
            """生成杨辉三角的一行"""
            row = [1]
            for k in range(n):
                row.append(row[k] * (n - k) // (k + 1))
            return row

        def get_bezier_coef(points: List[Tuple[float, float]]) -> List[float]:
            """计算贝塞尔曲线系数"""
            n = len(points) - 1
            coefficients = pascal_row(n)
            return coefficients
            
        def get_point_at_t(t: float, points: List[Tuple[float, float]], coefficients: List[float]) -> Tuple[float, float]:
            """计算贝塞尔曲线上某一点的坐标"""
            n = len(points) - 1
            x = y = 0
            for i, point in enumerate(points):
                x += coefficients[i] * point[0] * (t ** i) * ((1 - t) ** (n - i))
                y += coefficients[i] * point[1] * (t ** i) * ((1 - t) ** (n - i))
            return (x, y)

        coefficients = get_bezier_coef(points)
        track = []
        for i in range(num_points):
            t = i / (num_points - 1)
            point = get_point_at_t(t, points, coefficients)
            track.append(point)
            
        return track

    @staticmethod
    def add_human_noise(track: List[Tuple[float, float]], noise_amplitude: float = 2) -> List[Tuple[float, float]]:
        """添加模拟人类操作的随机抖动
        
        Args:
            track: 原始轨迹点列表
            noise_amplitude: 抖动幅度
            
        Returns:
            添加抖动后的轨迹点列表
        """
        noisy_track = []
        for x, y in track:
            # 使用更自然的噪声分布
            noise_x = random.gauss(0, noise_amplitude * 0.5) * (1 + random.random() * 0.2)
            noise_y = random.gauss(0, noise_amplitude * 0.2) * (1 + random.random() * 0.1)
            noisy_track.append((x + noise_x, y + noise_y))
        return noisy_track

    @staticmethod
    def generate_hover_track(start_point: Tuple[float, float], duration: float = 0.5) -> List[Tuple[float, float, float]]:
        """生成鼠标悬停时的微小移动轨迹
        
        Args:
            start_point: 起始坐标
            duration: 悬停持续时间(秒)
            
        Returns:
            悬停轨迹点列表，每个点是(x, y, t)坐标和时间
        """
        hover_track = []
        num_points = int(duration * 20)  # 每秒20个采样点
        x, y = start_point
        
        for i in range(num_points):
            progress = i / (num_points - 1)
            # 生成微小的圆形轨迹
            angle = progress * 2 * math.pi
            radius = random.uniform(0.2, 0.8)
            hover_x = x + math.cos(angle) * radius
            hover_y = y + math.sin(angle) * radius
            t = duration * progress
            hover_track.append((hover_x, hover_y, t))
            
        return hover_track

    @staticmethod
    def generate_slide_track(distance: float, duration: float = 1.0) -> List[Tuple[float, float, float]]:
        """生成完整的滑动轨迹，包含时间信息
        
        Args:
            distance: 目标滑动距离
            duration: 期望滑动持续时间(秒)
            
        Returns:
            轨迹点列表，每个点是(x, y, t)坐标和时间
        """
        # 生成多段式控制点
        segments = random.randint(3, 5)  # 2-4个中间控制点
        control_points = [(0, 0)]  # 起点
        
        # 生成中间控制点
        for i in range(segments - 1):
            progress = (i + 1) / segments
            x = distance * progress
            # 垂直方向的偏移随进度变化
            max_offset = 5 * (1 - abs(progress - 0.5))  # 中间段允许更大的偏移
            y = random.uniform(-max_offset, max_offset)
            control_points.append((x, y))
            
        control_points.append((distance, 0))  # 终点
        
        # 生成基础轨迹
        base_track = SlideTrackGenerator.bezier_curve(control_points, num_points=50)
        
        # 添加人类操作的随机抖动
        noisy_track = SlideTrackGenerator.add_human_noise(base_track)
        
        # 添加非线性时间分布和速度变化
        track_with_time = []
        for i, (x, y) in enumerate(noisy_track):
            progress = i / (len(noisy_track) - 1)
            
            # 使用改进的非线性时间分布
            if progress < 0.2:
                # 起始阶段：缓慢加速
                t = duration * (progress * 1.5)
            elif progress < 0.8:
                # 中间阶段：较快速度
                t = duration * (0.3 + (progress - 0.2) * 0.5)
            else:
                # 结束阶段：缓慢减速
                t = duration * (0.7 + (progress - 0.8) * 1.5)
                
            # 随机添加微小停顿
            if random.random() < 0.1:  # 10%的概率添加停顿
                t += random.uniform(0.01, 0.03)
                
            track_with_time.append((x, y, t))
            
        return track_with_time

    @staticmethod
    def generate_release_track(last_point: Tuple[float, float], release_duration: float = 0.2) -> List[Tuple[float, float, float]]:
        """生成释放时的轨迹，模拟手指抬起的自然动作
        
        Args:
            last_point: 最后一个滑动点的坐标
            release_duration: 释放持续时间(秒)
            
        Returns:
            释放轨迹点列表
        """
        x, y = last_point
        release_track = []
        
        # 生成更自然的释放轨迹
        num_points = random.randint(6, 8)  # 更多的采样点
        
        for i in range(num_points):
            progress = i / (num_points - 1)
            
            # 使用指数函数模拟手指抬起的加速度
            lift_progress = math.exp(progress) / math.exp(1)
            
            # 添加向上的移动，模拟手指抬起
            release_y = y - lift_progress * random.uniform(3, 5)
            
            # 添加水平方向的微小移动，模拟手指不稳定性
            release_x = x + random.gauss(0, 0.8) * (1 - progress)
            
            # 使用非线性时间分布
            t = release_duration * (1 - math.cos(progress * math.pi)) / 2
            
            release_track.append((release_x, release_y, t))
            
        return release_track

    @staticmethod
    def generate_complete_track(distance: float, total_duration: float = 2.0) -> List[Tuple[float, float, float]]:
        """生成完整的验证轨迹，包括准备、滑动和释放阶段
        
        Args:
            distance: 目标滑动距离
            total_duration: 总持续时间(秒)
            
        Returns:
            完整的轨迹点列表
        """
        # 分配各阶段时间
        hover_duration = total_duration * 0.2  # 20%时间用于悬停
        slide_duration = total_duration * 0.6  # 60%时间用于滑动
        release_duration = total_duration * 0.2  # 20%时间用于释放
        
        # 生成悬停轨迹
        hover_track = SlideTrackGenerator.generate_hover_track((0, 0), hover_duration)
        
        # 生成滑动轨迹
        slide_track = SlideTrackGenerator.generate_slide_track(distance, slide_duration)
        
        # 生成释放轨迹
        last_point = slide_track[-1][:2]  # 获取最后一个滑动点的坐标
        release_track = SlideTrackGenerator.generate_release_track(last_point, release_duration)
        
        # 组合完整轨迹
        complete_track = []
        
        # 添加悬停轨迹
        for x, y, t in hover_track:
            complete_track.append((x, y, t))
            
        # 添加滑动轨迹，时间需要加上悬停时间
        for x, y, t in slide_track:
            complete_track.append((x, y, t + hover_duration))
            
        # 添加释放轨迹，时间需要加上悬停和滑动时间
        for x, y, t in release_track:
            complete_track.append((x, y, t + hover_duration + slide_duration))
            
        return complete_track

if __name__ == "__main__":
    upload_file("./save_data/1.txt")
