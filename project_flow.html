<!DOCTYPE html>
<html lang="zh">
    <head>
        <meta charset="UTF-8" />
        <title>百度搜索爬虫项目流程图</title>
        <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
        <style>
            body {
                font-family: 'Microsoft YaHei', sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .flow-section {
                margin-bottom: 40px;
            }
            .description {
                margin: 20px 0;
                padding: 15px;
                background-color: #f8f9fa;
                border-left: 4px solid #007bff;
                border-radius: 4px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>百度搜索爬虫项目流程图</h1>

            <div class="flow-section">
                <h2>主要流程</h2>
                <div class="description">整体工作流程展示了从初始化到数据保存的完整过程。</div>
                <pre class="mermaid">
flowchart TB
    A[开始] --> B[初始化爬虫]
    B --> C[配置过滤规则]
    C --> D[启动Chrome浏览器]
    D --> E[处理搜索URL]
    E --> F{是否有更多URL}
    F -->|是| G[访问搜索页面]
    G --> H[处理搜索结果]
    H --> I[提取数据]
    I --> J[过滤和验证]
    J --> F
    F -->|否| K[保存结果]
    K --> L[结束]
                </pre>
            </div>

            <div class="flow-section">
                <h2>数据处理流程</h2>
                <div class="description">展示了单个搜索结果的处理流程。</div>
                <pre class="mermaid">
flowchart TB
    A[获取搜索结果] --> B[提取标题和链接]
    B --> C{标题过滤}
    C -->|通过| D[提取摘要]
    C -->|不通过| E[跳过]
    D --> F[提取发布单位]
    F --> G[获取实际链接]
    G --> H[提取TDK信息]
    H --> I[URL黑白名单过滤]
    I --> J{是否有效}
    J -->|是| K[保存结果]
    J -->|否| L[丢弃]
                </pre>
            </div>

            <div class="flow-section">
                <h2>验证和过滤机制</h2>
                <div class="description">展示了数据验证和过滤的详细流程。</div>
                <pre class="mermaid">
flowchart TB
    A[开始验证] --> B{检查标题过滤}
    B -->|启用| C[应用黑白名单规则]
    B -->|禁用| D[跳过标题过滤]
    C --> E{URL过滤}
    D --> E
    E -->|启用| F[检查域名白名单]
    E -->|禁用| G[跳过URL过滤]
    F --> H[检查URL黑名单]
    H --> I{重复检查}
    G --> I
    I -->|重复| J[跳过]
    I -->|不重复| K[保存]
                </pre>
            </div>
        </div>

        <script>
            mermaid.initialize({
                theme: 'default',
                flowchart: {
                    curve: 'basis',
                },
            })
        </script>
    </body>
</html>
