#!/usr/bin/env python3
"""
测试定时任务功能的脚本
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from database import Database

def test_database_tables():
    """测试数据库表是否正确创建"""
    print("测试数据库表创建...")
    
    db = Database()
    
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            
            # 检查定时任务表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='scheduled_tasks'")
            if cursor.fetchone():
                print("✓ scheduled_tasks 表已创建")
            else:
                print("✗ scheduled_tasks 表未找到")
                
            # 检查定时任务执行记录表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='scheduled_executions'")
            if cursor.fetchone():
                print("✓ scheduled_executions 表已创建")
            else:
                print("✗ scheduled_executions 表未找到")
                
            # 检查表结构
            cursor.execute("PRAGMA table_info(scheduled_tasks)")
            columns = [col[1] for col in cursor.fetchall()]
            expected_columns = ['id', 'name', 'config_id', 'schedule_type', 'schedule_config', 
                              'enabled', 'filter_config_id', 'url_filter_config_id', 
                              'skip_duplicate', 'skip_task_duplicate', 'created_at', 'updated_at']
            
            missing_columns = [col for col in expected_columns if col not in columns]
            if missing_columns:
                print(f"✗ scheduled_tasks 表缺少字段: {missing_columns}")
            else:
                print("✓ scheduled_tasks 表结构正确")
                
    except Exception as e:
        print(f"✗ 数据库测试失败: {e}")

def test_database_operations():
    """测试数据库操作"""
    print("\n测试数据库操作...")
    
    db = Database()
    
    try:
        # 测试保存定时任务
        schedule_config = {
            'hour': 9,
            'minute': 0
        }
        
        task_id = db.save_scheduled_task(
            name="测试任务",
            config_id=1,
            schedule_type="daily",
            schedule_config=json.dumps(schedule_config),
            skip_duplicate=True,
            skip_task_duplicate=True
        )
        
        if task_id:
            print(f"✓ 成功创建测试任务，ID: {task_id}")
            
            # 测试获取任务
            task = db.get_scheduled_task(task_id)
            if task:
                print(f"✓ 成功获取任务: {task[1]}")  # task[1] 是 name 字段
            else:
                print("✗ 获取任务失败")
                
            # 测试更新任务状态
            db.update_scheduled_task_status(task_id, False)
            print("✓ 成功更新任务状态")
            
            # 测试删除任务
            db.delete_scheduled_task(task_id)
            print("✓ 成功删除测试任务")
            
        else:
            print("✗ 创建测试任务失败")
            
    except Exception as e:
        print(f"✗ 数据库操作测试失败: {e}")

def test_scheduler_import():
    """测试调度器模块导入"""
    print("\n测试调度器模块导入...")
    
    try:
        # 尝试导入APScheduler
        from apscheduler.schedulers.background import BackgroundScheduler
        from apscheduler.triggers.cron import CronTrigger
        from apscheduler.triggers.interval import IntervalTrigger
        print("✓ APScheduler 模块导入成功")
        
        # 测试创建调度器
        scheduler = BackgroundScheduler()
        print("✓ 调度器创建成功")
        
        # 测试创建触发器
        cron_trigger = CronTrigger(hour=9, minute=0)
        interval_trigger = IntervalTrigger(hours=1)
        print("✓ 触发器创建成功")
        
    except ImportError as e:
        print(f"✗ APScheduler 模块导入失败: {e}")
        print("请运行: pip install APScheduler==3.10.4")
    except Exception as e:
        print(f"✗ 调度器测试失败: {e}")

def test_scheduler_module():
    """测试调度器模块"""
    print("\n测试调度器模块...")
    
    try:
        from scheduler import TaskScheduler
        print("✓ 调度器模块导入成功")
        
        # 创建调度器实例
        scheduler = TaskScheduler()
        print("✓ 调度器实例创建成功")
        
        # 测试创建触发器
        schedule_config = {'hour': 9, 'minute': 0}
        trigger = scheduler.create_trigger('daily', schedule_config)
        if trigger:
            print("✓ 触发器创建成功")
        else:
            print("✗ 触发器创建失败")
            
    except ImportError as e:
        print(f"✗ 调度器模块导入失败: {e}")
    except Exception as e:
        print(f"✗ 调度器模块测试失败: {e}")

def main():
    """主测试函数"""
    print("=" * 50)
    print("百度爬虫定时任务功能测试")
    print("=" * 50)
    
    test_database_tables()
    test_database_operations()
    test_scheduler_import()
    test_scheduler_module()
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
