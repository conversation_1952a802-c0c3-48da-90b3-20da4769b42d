# 百度搜索爬虫部署文档

## 中国大陆网络环境部署建议

> **如在中国大陆网络环境下部署，建议如下：**
>
> 1. 安装 uv 及依赖时，强烈建议使用国内 PyPI 镜像源，例如：
>     ```bash
>     pip install -i https://pypi.tuna.tsinghua.edu.cn/simple uv
>     uv pip install -r requirements.txt --index-url https://pypi.tuna.tsinghua.edu.cn/simple
>     ```
> 2. 若 chromedriver-autoinstaller 自动下载失败，请手动从 [清华镜像站](https://mirrors.tuna.tsinghua.edu.cn/chromedriver/) 下载与你本地 Chrome 版本对应的 ChromeDriver，并放到项目根目录或 PATH 路径下。
> 3. 或设置环境变量，令 chromedriver-autoinstaller 使用国内镜像：
>     - Linux/macOS:
>         ```bash
>         export CHROMEDRIVER_CDNURL="https://mirrors.tuna.tsinghua.edu.cn/chromedriver/"
>         ```
>     - Windows:
>         ```cmd
>         set CHROMEDRIVER_CDNURL=https://mirrors.tuna.tsinghua.edu.cn/chromedriver/
>         ```

## 环境要求

-   Python 3.8 或以上版本
-   推荐使用 [uv](https://github.com/astral-sh/uv) 进行依赖和环境管理
-   Chrome 浏览器（已安装即可，建议最新版）

## 快速部署步骤

### 1. 安装 Python

请确保已安装 Python 3.8 或更高版本，并已添加到 PATH。
如未安装，请访问 [Python 官网](https://www.python.org/downloads/)。

### 2. 安装 uv

uv 是现代 Python 包管理工具，支持跨平台自动化依赖管理。
安装命令（任选其一）：

```bash
pip install uv
# 或
pipx install uv
```

### 3. 安装依赖

uv 会自动创建虚拟环境并安装所有依赖（包括自动管理 ChromeDriver，无需手动下载）。

```bash
uv pip install -r requirements.txt
```

> 注意：本项目已集成 `chromedriver-autoinstaller`，首次运行时会自动下载并配置与本机 Chrome 版本匹配的 ChromeDriver，无需手动操作。

### 4. 配置项目

1. 打开 `config.py` 文件
2. 配置 `SEARCH_CONFIG` 列表中的搜索配置
3. 配置 `UPLOAD` 字典中的上传配置

### 5. 运行项目

```bash
uv pip run python start.py
```

或激活虚拟环境后直接运行：

```bash
python start.py
```

### 6. 结果说明与注意事项

-   运行后会在 `save_data` 目录下生成以时间戳命名的子文件夹，包含各关键词的 CSV 结果和汇总文件。
-   支持自动去重、黑名单过滤、微信通知、日志记录等功能，详见 config.py 配置项。
-   停止运行请按 Ctrl+C，程序会自动保存已获取结果。

### 7. 常见问题

-   确保 Chrome 浏览器已安装且为最新版
-   确保 Python 版本为 3.8 或以上
-   如遇依赖安装问题，优先使用 uv 重新安装依赖
-   其他问题请查阅项目 issues 或联系维护者
