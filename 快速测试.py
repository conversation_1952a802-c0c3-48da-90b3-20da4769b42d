#!/usr/bin/env python3
"""
定时任务功能快速测试脚本
运行此脚本可以快速验证定时任务功能是否正常工作
"""

import sys
import os
import json
import time
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_functionality():
    """测试基本功能"""
    print("🔧 测试基本功能...")
    
    try:
        # 测试数据库
        from database import Database
        db = Database()
        print("✅ 数据库连接正常")
        
        # 测试调度器
        from scheduler import TaskScheduler
        scheduler = TaskScheduler()
        print("✅ 调度器创建正常")
        
        return True
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def create_test_config():
    """创建测试配置"""
    print("\n📝 创建测试配置...")
    
    try:
        from database import Database
        db = Database()
        
        # 创建测试URL配置
        test_urls = [
            'https://www.baidu.com/s?wd=python+测试',
            'https://www.baidu.com/s?wd=定时任务+测试'
        ]
        
        config_id = db.save_config("测试配置", json.dumps(test_urls, ensure_ascii=False))
        print(f"✅ 测试配置创建成功，ID: {config_id}")
        return config_id
    except Exception as e:
        print(f"❌ 创建测试配置失败: {e}")
        return None

def create_test_task(config_id):
    """创建测试任务"""
    print("\n⏰ 创建测试任务...")
    
    try:
        from scheduler import TaskScheduler
        scheduler = TaskScheduler()
        
        # 创建一个1分钟后执行的任务
        future_time = datetime.now() + timedelta(minutes=1)
        
        schedule_config = {
            'hour': future_time.hour,
            'minute': future_time.minute
        }
        
        task_id = scheduler.add_task(
            name="快速测试任务",
            config_id=config_id,
            schedule_type="daily",
            schedule_config=schedule_config
        )
        
        print(f"✅ 测试任务创建成功，ID: {task_id}")
        print(f"📅 任务将在 {future_time.strftime('%H:%M')} 执行")
        return task_id
    except Exception as e:
        print(f"❌ 创建测试任务失败: {e}")
        return None

def monitor_task(task_id, duration=120):
    """监控任务执行"""
    print(f"\n👀 监控任务执行 {duration} 秒...")
    
    try:
        from database import Database
        db = Database()
        
        start_time = time.time()
        last_execution_count = 0
        
        while time.time() - start_time < duration:
            # 检查执行记录
            executions = db.get_scheduled_executions(task_id, 5)
            current_count = len(executions)
            
            if current_count > last_execution_count:
                print(f"🎉 检测到新的执行记录！")
                for execution in executions[:current_count - last_execution_count]:
                    print(f"   执行ID: {execution[2]}")
                    print(f"   状态: {execution[3]}")
                    print(f"   开始时间: {execution[4]}")
                last_execution_count = current_count
            
            # 显示等待状态
            elapsed = int(time.time() - start_time)
            print(f"\r⏳ 等待中... {elapsed}/{duration}秒", end="", flush=True)
            time.sleep(5)
        
        print(f"\n⏰ 监控结束")
        return last_execution_count > 0
        
    except Exception as e:
        print(f"\n❌ 监控任务失败: {e}")
        return False

def cleanup_test_data(config_id, task_id):
    """清理测试数据"""
    print("\n🧹 清理测试数据...")
    
    try:
        from database import Database
        from scheduler import TaskScheduler
        
        db = Database()
        scheduler = TaskScheduler()
        
        # 删除测试任务
        if task_id:
            scheduler.delete_task(task_id)
            print("✅ 测试任务已删除")
        
        # 删除测试配置
        if config_id:
            db.delete_config(config_id)
            print("✅ 测试配置已删除")
            
    except Exception as e:
        print(f"❌ 清理测试数据失败: {e}")

def main():
    """主测试函数"""
    print("=" * 60)
    print("🚀 百度爬虫定时任务功能快速测试")
    print("=" * 60)
    
    # 测试基本功能
    if not test_basic_functionality():
        print("\n❌ 基本功能测试失败，请检查依赖安装")
        return
    
    # 创建测试配置
    config_id = create_test_config()
    if not config_id:
        print("\n❌ 无法创建测试配置")
        return
    
    # 创建测试任务
    task_id = create_test_task(config_id)
    if not task_id:
        print("\n❌ 无法创建测试任务")
        cleanup_test_data(config_id, None)
        return
    
    print("\n" + "=" * 60)
    print("📋 测试说明：")
    print("1. 已创建一个测试任务，将在1分钟后执行")
    print("2. 脚本会监控任务执行情况")
    print("3. 注意：这只是调度测试，不会实际运行爬虫")
    print("4. 实际的爬虫执行需要在Web界面中进行")
    print("=" * 60)
    
    # 询问是否继续监控
    response = input("\n是否开始监控测试？(y/n): ").lower().strip()
    
    if response == 'y':
        # 监控任务执行
        success = monitor_task(task_id)
        
        if success:
            print("\n🎉 测试成功！定时任务功能正常工作")
        else:
            print("\n⚠️  未检测到任务执行，可能需要更长时间")
    else:
        print("\n⏭️  跳过监控测试")
    
    # 清理测试数据
    cleanup_test_data(config_id, task_id)
    
    print("\n" + "=" * 60)
    print("✅ 快速测试完成！")
    print("\n📋 接下来您可以：")
    print("1. 启动Web服务：python start.py")
    print("2. 访问：http://127.0.0.1:5000")
    print("3. 点击'定时任务'标签页开始使用")
    print("=" * 60)

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⏹️  测试被用户中断")
    except Exception as e:
        print(f"\n\n❌ 测试过程中出现错误: {e}")
        print("请检查依赖安装和项目配置")
