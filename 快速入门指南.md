# 定时任务功能 - 5 分钟快速入门

## 🎯 这个功能是干什么的？

**简单来说：让爬虫自动定时运行，您不用手动操作！**

比如：

-   每天早上 9 点自动爬取新闻
-   每周一自动爬取招聘信息
-   每 2 小时自动爬取股价信息

## 🚀 只需 4 步，立即上手

### 第 1 步：启动程序

```bash
python start.py
```

然后打开浏览器访问：http://127.0.0.1:5000

### 第 2 步：准备爬虫配置

1. 点击 **"爬虫配置"** 标签页
2. 在 URL 框里输入要爬的网址，比如：
    ```
    https://www.baidu.com/s?wd=python教程
    https://www.baidu.com/s?wd=机器学习
    ```
3. 点击 **"保存当前配置"**，起个名字如"Python 学习资料"

### 第 3 步：创建定时任务

1. 点击 **"定时任务"** 标签页
2. 点击 **"创建定时任务"** 按钮
3. 填写信息：
    ```
    任务名称：每日Python学习资料爬取
    爬虫配置：选择"Python学习资料"
    调度类型：每日执行
    时间：09:00（上午9点）
    ```
4. 点击 **"创建任务"**

### 第 4 步：查看结果

-   任务会自动在每天 9 点运行
-   点击 **"搜索结果"** 标签页查看爬取的数据
-   点击 **"执行记录"** 查看任务运行历史

## 📅 4 种定时模式

### 🕘 每日执行

**用途**：每天固定时间执行
**设置**：选择小时和分钟
**示例**：每天上午 9 点

### 📆 每周执行

**用途**：每周固定时间执行
**设置**：选择星期几 + 时间
**示例**：每周一上午 8 点

### ⏱️ 间隔执行

**用途**：按固定间隔重复执行
**设置**：设置间隔时间
**示例**：每 2 小时执行一次

### ⚙️ 自定义 Cron

**用途**：复杂的时间规则
**设置**：输入 Cron 表达式
**示例**：`0 9 * * *`（每天 9 点）

## 💡 实用小贴士

### ✅ 推荐设置

-   **新闻爬取**：每天 1-2 次就够了
-   **招聘信息**：每天 1 次或每周几次
-   **实时数据**：每小时 1 次
-   **勾选去重选项**：避免重复数据

### ⚠️ 注意事项

-   不要设置过于频繁的执行（比如每分钟一次）
-   定期查看执行记录，确保任务正常运行
-   如果任务失败，查看错误信息并调整配置

## 🎯 3 个实用例子

### 例子 1：每日新闻爬取

```
任务名称：每日科技新闻
URL配置：https://www.baidu.com/s?wd=科技新闻
调度：每日执行 08:00
```

### 例子 2：每周招聘信息

```
任务名称：程序员招聘
URL配置：https://www.baidu.com/s?wd=程序员招聘
调度：每周执行 周一 09:00
```

### 例子 3：股价监控

```
任务名称：股价监控
URL配置：https://www.baidu.com/s?wd=股价+今日
调度：间隔执行 每2小时
```

## 🔧 管理您的任务

### 查看任务列表

在"定时任务"页面可以看到所有任务，包括：

-   任务名称和配置
-   执行时间设置
-   当前状态（启用/禁用）

### 控制任务

-   **启用/禁用**：随时开关任务
-   **查看记录**：看任务执行历史
-   **删除任务**：删除不需要的任务

### 查看结果

-   **搜索结果页面**：看到所有爬取的数据
-   **执行记录页面**：看到任务运行状态

## ❓ 遇到问题？

### 任务不执行？

1. 检查任务是否启用（绿色=启用，灰色=禁用）
2. 确认时间设置正确
3. 查看执行记录的错误信息

### 没有数据？

1. 检查 URL 配置是否正确
2. 确认网络连接正常
3. 查看执行记录是否有错误

### 数据重复？

1. 勾选"过滤重复链接"
2. 勾选"任务内去重"
3. 设置合适的执行频率

## 🎉 开始使用吧！

现在您已经掌握了定时任务的基本用法，可以：

1. **立即尝试**：创建一个简单的每日任务
2. **逐步优化**：根据需要调整时间和配置
3. **监控结果**：定期查看执行情况

**记住**：定时任务会在后台自动运行，您只需要设置一次，就可以持续获得最新数据！

---

💡 **提示**：如果您是第一次使用，建议先创建一个简单的每日任务进行测试，熟悉流程后再创建更复杂的任务。
