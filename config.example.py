# 基础配置
class Config:
    # 文件保存配置
    SAVE_DATA_DIR = 'save_data'  # 数据保存的根目录

    USER_AGENTS = [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/58.0.3029.110 Safari/537.3",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Safari/605.1.15",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (iPad; CPU OS 14_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0.3 Mobile/15E148 Safari/604.1",
        "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/77.0.3865.92 Mobile Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; WOW64; Trident/7.0; rv:11.0) like Gecko",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Safari/537.36",
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_4 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36",
        "Mozilla/5.0 (iPad; CPU OS 14_4 like Mac OS X) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36",
        "Mozilla/5.0 (Linux; Android 11; SM-G975F) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/87.0.4280.88 Mobile Safari/537.36"
    ]
    
    # 百度搜索配置
    BAIDU = {
        'BASE_URL': 'https://www.baidu.com/s?',  # 百度搜索基础URL
        'SEARCH_TIME_RANGE': {
            'DAYS': 3  # 搜索最近几天的数据
        },
        # 页面元素选择器
        'SELECTORS': {
            'RESULT': {
                'CONTAINER': 'div.result.c-container',  # 搜索结果容器
                'TITLE': 'h3.c-title a',  # 标题元素
                'ABSTRACT': '.content-right_1THTn',  # 摘要元素
                'PUBLISH_UNIT': 'div.source_1Vdff span.c-color-gray',  # 发布单位元素
                'TOOLS': 'c-tools'  # 工具栏元素(用于获取完整标题)
            },
            'PAGINATION': {
                'CONTAINER': 'page',  # 分页容器ID
                'NEXT_BUTTON': '#page a.n'  # 下一页按钮
            },
            'VERIFICATION': {
                'SLIDER': 'passMod_dialog-mask'  # 滑块验证码class
            }
        }
    }
    
    # 文件处理配置
    FILE = {
        'CSV_FIELDS': [
            'title',         # 标题
            'full_title',    # 全标题
            'link',          # 实际链接
            'abstract',      # 摘要
            'publish_unit',  # 发布单位
            'search_url',    # 搜索的链接
            'wd'            # 搜索的关键字
        ],
        'CSV_HEADERS': {    # 中文表头映射
            'title': '标题',
            'full_title': '全标题',
            'link': '实际链接',
            'abstract': '摘要',
            'publish_unit': '发布单位',
            'search_url': '搜索的链接',
            'wd': '搜索的关键字'
        }
    }
    
    # 上传配置
    UPLOAD = {
        'API_URL': 'https://your-api-endpoint.com/upload',  # 替换为实际的上传URL
        'TOKEN': 'your_token_here',  # 上传认证token
        'TIMEOUT': 30  # 上传超时时间(秒)
    }
    
    # 微信机器人配置
    WECHAT = {
        'BOT_URL': 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=your_key_here'  # 机器人webhook地址
    }
    
    # 链接替换文案数组
    LINK_REPLACE_TITLES = [
        "(Caused by SSLError(SSLError(1, '[SSL: DH_KEY_TOO_SMALL] dh key too small (_ssl.c:1010",
        "(Caused by SSLError(SSLError(1, '[SSL: UNSAFE_LEGACY_RENEGOTIATION_DISABLED] unsafe legacy renegotiation disabled (_ssl.c:1010",
        "(Caused by SSLError(SSLError(1, '[SSL: BAD_ECPOINT] bad ecpoint (_ssl.c:1010",
        "(Caused by SSLError(SSLError(1, '[SSL: SSLV3_ALERT_HANDSHAKE_FAILURE] sslv3 alert handshake failure (_ssl.c:1010",
        "(Caused by SSLError(SSLEOFError(8, '[SSL: UNEXPECTED_EOF_WHILE_READING] EOF occurred in violation of protocol (_ssl.c:1010",
    ]

    # 搜索关键词配置列表
    SEARCH_QUERIES = [
        'https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&tn=baiduadv&wd=title%3A%20(2025%20%22%E5%8D%9A%E5%A3%AB%20%E5%8D%9A%E5%A3%AB%E5%90%8E%22%20(%E6%8B%9B%E8%81%98)%20-(%E5%B0%B1%E4%B8%9A%E7%BD%91%20%7C%20%E5%85%AC%E7%A4%BA))%20site%3Aedu.cn&ct=2097152&rn=50&si=edu.cn',
        # 其他搜索URL...
    ] 


    # 得塔云key https://bq1gpmr8.xiaomy.net/
    IMG_IDENTIFICATION_KEY = 'xxx'