import tkinter as tk
from tkinter import ttk, messagebox
import json
from database import Database
from config import Config
from browser_search import BaiduCrawler
import threading
import os

class CrawlerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("百度搜索爬虫配置")
        self.root.geometry("800x600")
        
        self.db = Database()
        
        # 创建主框架
        self.main_frame = ttk.Frame(self.root, padding="10")
        self.main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # URL输入区域
        self.url_label = ttk.Label(self.main_frame, text="搜索URL列表 (每行一个):")
        self.url_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        # URL文本框
        self.url_text = tk.Text(self.main_frame, height=20, width=80)
        self.url_text.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        self.scrollbar = ttk.Scrollbar(self.main_frame, orient=tk.VERTICAL, command=self.url_text.yview)
        self.scrollbar.grid(row=1, column=2, sticky=(tk.N, tk.S))
        self.url_text['yscrollcommand'] = self.scrollbar.set
        
        # 按钮框架
        self.button_frame = ttk.Frame(self.main_frame)
        self.button_frame.grid(row=2, column=0, columnspan=2, pady=10)
        
        # 加载配置按钮
        self.load_btn = ttk.Button(self.button_frame, text="加载配置", command=self.load_config)
        self.load_btn.pack(side=tk.LEFT, padx=5)
        
        # 保存配置按钮
        self.save_btn = ttk.Button(self.button_frame, text="保存配置", command=self.save_config)
        self.save_btn.pack(side=tk.LEFT, padx=5)
        
        # 开始爬取按钮
        self.start_btn = ttk.Button(self.button_frame, text="开始爬取", command=self.start_crawler)
        self.start_btn.pack(side=tk.LEFT, padx=5)
        
        # 状态标签
        self.status_label = ttk.Label(self.main_frame, text="就绪")
        self.status_label.grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # 加载最近的配置
        self.load_config()

    def load_config(self):
        """加载配置"""
        try:
            # 从配置文件加载
            if os.path.exists('last_config.json'):
                with open('last_config.json', 'r', encoding='utf-8') as f:
                    urls = json.load(f)
                self.url_text.delete('1.0', tk.END)
                self.url_text.insert('1.0', '\n'.join(urls))
                self.status_label['text'] = "配置加载成功"
            else:
                # 如果配置文件不存在，加载默认配置
                self.url_text.delete('1.0', tk.END)
                self.url_text.insert('1.0', '\n'.join(Config.SEARCH_QUERIES))
                self.status_label['text'] = "已加载默认配置"
        except Exception as e:
            messagebox.showerror("错误", f"加载配置失败: {str(e)}")

    def save_config(self):
        """保存配置"""
        try:
            # 获取文本框中的URL
            urls = self.url_text.get('1.0', tk.END).strip().split('\n')
            urls = [url.strip() for url in urls if url.strip()]
            
            # 保存到配置文件
            with open('last_config.json', 'w', encoding='utf-8') as f:
                json.dump(urls, f, ensure_ascii=False, indent=2)
            
            # 更新Config中的配置
            Config.SEARCH_QUERIES = urls
            
            self.status_label['text'] = "配置已保存"
            messagebox.showinfo("成功", "配置已保存")
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")

    def start_crawler(self):
        """启动爬虫"""
        try:
            # 保存当前配置
            self.save_config()
            
            # 禁用按钮
            self.start_btn['state'] = 'disabled'
            self.load_btn['state'] = 'disabled'
            self.save_btn['state'] = 'disabled'
            
            # 创建爬虫线程
            crawler_thread = threading.Thread(target=self.run_crawler)
            crawler_thread.daemon = True
            crawler_thread.start()
            
            self.status_label['text'] = "爬虫运行中..."
        except Exception as e:
            messagebox.showerror("错误", f"启动爬虫失败: {str(e)}")
            self.enable_buttons()

    def run_crawler(self):
        """运行爬虫"""
        try:
            crawler = BaiduCrawler()
            crawler.run()
            self.root.after(0, self.crawler_finished, True)
        except Exception as e:
            self.root.after(0, self.crawler_finished, False, str(e))

    def crawler_finished(self, success, error_msg=None):
        """爬虫完成回调"""
        self.enable_buttons()
        if success:
            self.status_label['text'] = "爬虫运行完成"
            messagebox.showinfo("完成", "爬虫运行完成")
        else:
            self.status_label['text'] = "爬虫运行出错"
            messagebox.showerror("错误", f"爬虫运行出错: {error_msg}")

    def enable_buttons(self):
        """启用按钮"""
        self.start_btn['state'] = 'normal'
        self.load_btn['state'] = 'normal'
        self.save_btn['state'] = 'normal'

def main():
    root = tk.Tk()
    app = CrawlerGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main() 