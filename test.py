# from util import fix_url_to_baidu
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.wait import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import json
import os
from datetime import datetime
import time
import random
import re
import base64  # 使用Python内置的base64模块
from config import Config
from database import Database
from urllib.parse import urlparse
import requests
from urllib.parse import parse_qs
import csv
from util import fix_url_to_baidu
from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from webdriver_manager.chrome import ChromeDriverManager
import urllib3
from bs4 import BeautifulSoup
import hashlib
import platform
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# fix_url_to_baidu("https://www.baidu.com/s?ie=utf-8&f=8&rsv_bp=1&tn=baiduadv&wd=title%3A%20(2025%20%22%E5%8D%9A%E5%A3%AB%20%E5%8D%9A%E5%A3%AB%E5%90%8E%22%20(%E6%8B%9B%E8%81%98)%20-(%E5%B0%B1%E4%B8%9A%E7%BD%91%20%7C%20%E5%85%AC%E7%A4%BA))%20site%3Aedu.cn&ct=2097152&rn=50&si=edu.cn&oq=title%253A%2520(2024%2520%2526quot%253B%25E5%258D%259A%25E5%25A3%25AB%2520%25E5%258D%259A%25E5%25A3%25AB%25E5%2590%258E%2526quot%253B%2520(%25E6%258B%259B%25E8%2581%2598)%2520-(%25E5%25B0%25B1%25E4%25B8%259A%25E7%25BD%2591%2520%257C%2520%25E5%2585%25AC%25E7%25A4%25BA))%2520site%253Aedu.cn&rsv_pq=f710460400688c7b&rsv_t=ea9dHCeK4FNmnudpTi4k5dVYZ4ocmiAzQ433ciEdevQPb38DuKAFrzMOeE%2B0Tz0&rqlang=cn&rsv_enter=1&rsv_dl=tb&rsv_sug3=2&rsv_sug1=2&rsv_sug7=100&rsv_sug2=0&rsv_btype=t&inputT=683&rsv_sug4=1202")

# fix_url_to_baidu("https://www.baidu.com/s?wd=site:(gov.cn) title:(招聘 (公告 | 简章))&rn=50	")

# rs = re.search("1902624671250104322$","https://jy.bsu.edu.cn/front/zwxx.jspa?xqzwId=1902624499824705537&zpxxId=1902624671250104322")

# if rs:
#     print("匹配")
# else:
#     print("不匹配")


def get_info_from_html(html):
    """从HTML中提取信息(暂时获取tdk)"""
    try:
        soup = BeautifulSoup(html, 'html.parser')
        url_title = soup.title.string if soup.title else ""
        url_article_title = ""
        
        # 查找ArticleTitle meta标签
        article_title_meta = soup.find("meta", attrs={"name": "ArticleTitle"})
        if article_title_meta and article_title_meta.get('content'):
            url_article_title = article_title_meta.get('content')

        # 清理标题中的空白字符
        url_title = url_title.replace(" ", "").replace("\n", "") if url_title else ""
        url_article_title = url_article_title.replace(" ", "").replace("\n", "") if url_article_title else ""

        # 修改日志输出方式，将多个参数合并成一个字符串
        print(f"Title: {url_title}")
        print(f"ArticleTitle: {url_article_title}")
        
        return url_title, url_article_title
    except Exception as e:
        print(f"提取HTML信息时出错: {str(e)}")
        return '', ''


url = 'https://agronomy.swu.edu.cn/info/1067/5902.htm'


headers = {'User-Agent': random.choice(Config.USER_AGENTS)}
response = requests.get(url, headers=headers, allow_redirects=True, timeout=5, verify=False)
response.encoding = response.apparent_encoding  # 自动检测编码，防止中文乱码
url = response.url

                        
html = response.text
url_title, url_article_title = get_info_from_html(html)

print(url_title, url_article_title)