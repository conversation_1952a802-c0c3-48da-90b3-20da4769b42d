# 百度搜索爬虫定时任务功能使用指南

## 🎯 功能介绍

定时任务功能让您可以设置自动执行的爬虫任务，无需手动操作。系统会在指定时间自动启动爬虫，抓取数据并保存结果。

**主要功能：**

-   ⏰ 定时自动执行爬虫任务
-   📅 支持多种时间调度模式
-   🔧 使用已保存的爬虫配置
-   📊 查看执行历史和结果
-   🎛️ 可视化管理界面

## 🚀 快速开始

### 第一步：安装依赖

```bash
pip install APScheduler==3.10.4
```

### 第二步：启动服务

```bash
python start.py
```

### 第三步：打开浏览器

访问：http://127.0.0.1:5000

### 第四步：进入定时任务页面

点击页面顶部的 **"定时任务"** 标签页

## 📖 详细使用教程

### 步骤 1：准备爬虫配置

在创建定时任务之前，您需要先有一个爬虫配置：

1. **进入爬虫配置页面**

    - 点击 **"爬虫配置"** 标签页

2. **设置 URL 列表**

    - 在 URL 输入框中输入要爬取的搜索 URL
    - 例如：`https://www.baidu.com/s?wd=python教程`

3. **保存配置**
    - 点击 **"保存当前配置"** 按钮
    - 输入配置名称（如："Python 教程爬取"）
    - 点击保存

> 💡 **提示**：爬虫配置包含了要爬取的 URL 列表，定时任务会使用这些配置来执行爬虫。

### 步骤 2：创建定时任务

1. **进入定时任务页面**

    - 点击页面顶部的 **"定时任务"** 标签

2. **点击创建按钮**

    - 点击右上角的 **"创建定时任务"** 按钮

3. **填写基本信息**

    ```
    任务名称：Python教程定时爬取
    爬虫配置：选择刚才保存的"Python教程爬取"配置
    ```

4. **选择调度类型**（四种模式任选其一）

    **🕘 每日执行**

    ```
    调度类型：每日执行
    小时：09
    分钟：00
    说明：每天上午9点执行
    ```

    **📅 每周执行**

    ```
    调度类型：每周执行
    星期：星期一
    小时：08
    分钟：00
    说明：每周一上午8点执行
    ```

    **⏱️ 间隔执行**

    ```
    调度类型：间隔执行
    间隔数值：2
    间隔类型：小时
    说明：每2小时执行一次
    ```

    **⚙️ 自定义 Cron**

    ```
    调度类型：自定义Cron
    Cron表达式：0 */6 * * *
    说明：每6小时执行一次
    ```

5. **可选配置**

    - 文案过滤配置：选择已保存的过滤规则（可选）
    - URL 过滤配置：选择已保存的 URL 过滤规则（可选）
    - 过滤重复链接：✅ 建议勾选
    - 任务内去重：✅ 建议勾选

6. **创建任务**
    - 点击 **"创建任务"** 按钮
    - 看到"定时任务创建成功"提示

### 步骤 3：管理定时任务

创建完成后，您可以在任务列表中看到刚创建的任务：

**任务列表显示信息：**

-   任务名称
-   使用的配置
-   调度类型和时间
-   当前状态（启用/禁用）
-   创建时间

**可执行操作：**

-   **✅ 启用/禁用**：点击对应按钮来启用或禁用任务
-   **📊 执行记录**：点击查看任务运行历史
-   **🗑️ 删除**：删除不需要的任务

### 步骤 4：查看执行结果

1. **查看执行记录**

    - 在任务列表中点击 **"执行记录"** 按钮
    - 可以看到：
        - 执行时间
        - 执行状态（运行中/已完成/失败）
        - 抓取结果数量
        - 错误信息（如果有）

2. **查看爬取数据**
    - 点击 **"搜索结果"** 标签页
    - 可以看到定时任务爬取的所有数据
    - 数据包括：标题、链接、摘要、发布单位等

## 🔧 调度类型详解

### 每日执行

-   **适用场景**：每天定时爬取新闻、公告等
-   **设置方法**：选择小时（0-23）和分钟（0-59）
-   **示例**：每天上午 9 点执行

### 每周执行

-   **适用场景**：每周定时爬取周报、统计数据等
-   **设置方法**：选择星期几 + 具体时间
-   **示例**：每周一上午 8 点执行

### 间隔执行

-   **适用场景**：需要频繁更新的数据
-   **设置方法**：设置间隔数值和单位（分钟/小时/天）
-   **示例**：每 2 小时执行一次

### 自定义 Cron

-   **适用场景**：复杂的时间规则
-   **格式**：`分 时 日 月 星期`
-   **常用示例**：
    -   `0 9 * * *` - 每天 9 点
    -   `0 9 * * 1` - 每周一 9 点
    -   `0 */6 * * *` - 每 6 小时
    -   `30 8 1 * *` - 每月 1 号 8:30

## 💡 使用技巧

### 1. 合理设置执行频率

-   新闻类：每天 1-2 次
-   公告类：每天 1 次
-   实时数据：每小时 1 次
-   避免过于频繁，以免对目标网站造成压力

### 2. 使用过滤配置

-   设置文案过滤：过滤掉不相关的内容
-   设置 URL 过滤：只爬取特定域名的链接
-   启用去重功能：避免重复数据

### 3. 监控执行状态

-   定期查看执行记录
-   关注失败的任务并及时处理
-   根据结果调整爬虫配置

## ❓ 常见问题

### Q1：任务创建后不执行怎么办？

**A1：** 检查以下几点：

1. 任务是否已启用
2. 调度时间设置是否正确
3. 查看执行记录中的错误信息

### Q2：如何修改已创建的任务？

**A2：** 目前需要删除原任务，重新创建新任务

### Q3：任务执行失败怎么办？

**A3：**

1. 查看执行记录中的错误信息
2. 检查网络连接
3. 确认目标网站是否可访问
4. 检查爬虫配置是否正确

### Q4：可以同时运行多个任务吗？

**A4：** 可以，但建议避免同时运行过多任务，以免影响系统性能

## 🎯 实际应用示例

### 示例 1：每日新闻爬取

```
任务名称：每日科技新闻
爬虫配置：科技新闻URL列表
调度类型：每日执行
执行时间：08:00
```

### 示例 2：学术论文监控

```
任务名称：AI论文监控
爬虫配置：学术搜索URL
调度类型：每周执行
执行时间：周一 09:00
```

### 示例 3：实时资讯更新

```
任务名称：实时资讯
爬虫配置：新闻资讯URL
调度类型：间隔执行
间隔：4小时
```

现在您就可以轻松使用定时任务功能了！🎉
