# 百度搜索爬虫定时任务功能使用指南

## 🎯 功能概述

定时任务功能让您可以设置自动执行的爬虫任务，无需手动操作。系统会在指定时间自动启动爬虫，抓取数据并保存结果。

**主要功能：**

-   ⏰ 定时自动执行爬虫任务
-   📅 支持多种时间调度模式
-   🔧 使用已保存的爬虫配置
-   📊 查看执行历史和结果
-   🎛️ 可视化管理界面

## 🚀 快速开始

### 第一步：安装依赖

```bash
pip install APScheduler==3.10.4
```

### 第二步：启动服务

```bash
python main.py
```

### 第三步：打开浏览器

访问：http://127.0.0.1:5511

### 第四步：进入定时任务页面

点击页面顶部的 **"定时任务"** 标签页

## 功能使用

### 1. 访问定时任务界面

启动 Web 服务后，在浏览器中访问主页面，点击"定时任务"标签页。

### 2. 创建定时任务

1. 点击"创建定时任务"按钮
2. 填写任务信息：
    - **任务名称**: 给任务起一个有意义的名称
    - **爬虫配置**: 选择要使用的爬虫配置（URL 列表）
    - **文案过滤配置**: 可选，选择文案过滤规则
    - **URL 过滤配置**: 可选，选择 URL 过滤规则
    - **调度类型**: 选择执行频率

### 3. 调度类型说明

#### 每日执行

-   每天在指定时间执行一次
-   设置小时（0-23）和分钟（0-59）

#### 每周执行

-   每周在指定的星期几和时间执行
-   设置星期几、小时和分钟

#### 间隔执行

-   按固定间隔重复执行
-   支持分钟、小时、天为单位
-   设置间隔数值和类型

#### 自定义 Cron

-   使用 Cron 表达式进行精确控制
-   格式：`分 时 日 月 星期`
-   例如：`0 9 * * *` 表示每天 9 点执行

### 4. 任务管理

-   **启用/禁用**: 可以随时启用或禁用任务
-   **查看执行记录**: 查看任务的历史执行情况
-   **删除任务**: 删除不需要的任务

### 5. 执行记录

系统会记录每次定时任务的执行情况，包括：

-   执行时间
-   执行状态（运行中、已完成、失败）
-   结果数量
-   错误信息（如果有）

## 技术实现

### 数据库表结构

#### scheduled_tasks（定时任务表）

-   `id`: 任务 ID
-   `name`: 任务名称
-   `config_id`: 关联的爬虫配置 ID
-   `schedule_type`: 调度类型
-   `schedule_config`: 调度配置（JSON 格式）
-   `enabled`: 是否启用
-   `filter_config_id`: 文案过滤配置 ID
-   `url_filter_config_id`: URL 过滤配置 ID
-   `skip_duplicate`: 是否过滤重复链接
-   `skip_task_duplicate`: 是否任务内去重
-   `created_at`: 创建时间
-   `updated_at`: 更新时间

#### scheduled_executions（执行记录表）

-   `id`: 记录 ID
-   `task_id`: 关联的任务 ID
-   `execution_key`: 执行标识
-   `status`: 执行状态
-   `start_time`: 开始时间
-   `end_time`: 结束时间
-   `error_message`: 错误信息
-   `results_count`: 结果数量

### 核心模块

#### scheduler.py

-   `TaskScheduler`: 主调度器类
-   负责任务的调度和执行
-   使用 APScheduler 进行任务调度

#### web_gui.py

-   新增定时任务相关的 API 接口
-   `/api/scheduled-tasks`: 任务管理
-   `/api/scheduled-executions`: 执行记录

#### templates/index.html

-   新增定时任务管理界面
-   任务创建、列表、执行记录展示

## 使用示例

### 创建每日定时任务

1. 任务名称：每日博士招聘信息爬取
2. 选择配置：博士招聘配置
3. 调度类型：每日执行
4. 执行时间：09:00

### 创建每周定时任务

1. 任务名称：周一学术会议信息爬取
2. 选择配置：学术会议配置
3. 调度类型：每周执行
4. 执行时间：星期一 08:00

### 创建间隔任务

1. 任务名称：每 2 小时新闻爬取
2. 选择配置：新闻配置
3. 调度类型：间隔执行
4. 间隔设置：2 小时

## 注意事项

1. **服务器时间**: 定时任务基于服务器时间执行
2. **任务冲突**: 同一时间只能有一个相同任务实例运行
3. **错误处理**: 任务执行失败会记录错误信息，不会影响后续执行
4. **资源占用**: 长时间运行的爬虫任务可能占用较多资源
5. **数据备份**: 建议定期备份数据库文件

## 故障排除

### 任务不执行

1. 检查任务是否启用
2. 检查调度配置是否正确
3. 查看执行记录中的错误信息

### 依赖问题

1. 确保已安装 APScheduler 库
2. 检查 Python 版本兼容性

### 数据库问题

1. 检查数据库文件权限
2. 运行测试脚本验证数据库表结构

## 测试

运行测试脚本验证功能：

```bash
python test_scheduler.py
```

该脚本会测试：

-   数据库表创建
-   数据库操作
-   调度器模块导入
-   基本功能测试
